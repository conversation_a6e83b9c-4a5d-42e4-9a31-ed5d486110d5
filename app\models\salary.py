from datetime import datetime
from app import db
import json

class Employee(db.Model):
    """教职工信息表"""
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False, index=True)  # 工号
    name = db.Column(db.String(50), nullable=False, index=True)  # 姓名
    school_type = db.Column(db.String(20), nullable=False)  # 学校类型（中学/小学）
    department = db.Column(db.String(50))  # 部门
    position = db.Column(db.String(50))  # 职位
    
    # 关联工资记录
    salary_records = db.relationship('SalaryRecord', backref='employee', lazy=True)

    def __repr__(self):
        return f'<Employee {self.name}>'

class SalaryRecord(db.Model):
    """工资记录表"""
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('employee.id'), nullable=False)
    salary_date = db.Column(db.Date, nullable=False, index=True)  # 工资月份
    
    # 基本工资项
    base_salary = db.Column(db.Float)  # 基本工资
    position_salary = db.Column(db.Float)  # 职务工资
    seniority_salary = db.Column(db.Float)  # 工龄工资
    performance_salary = db.Column(db.Float)  # 绩效工资
    
    # 补贴项
    teaching_allowance = db.Column(db.Float)  # 教学补贴
    class_management_allowance = db.Column(db.Float)  # 班主任补贴
    overtime_allowance = db.Column(db.Float)  # 加班补贴
    other_allowance = db.Column(db.Float)  # 其他补贴
    
    # 扣除项
    insurance_deduction = db.Column(db.Float)  # 保险扣除
    fund_deduction = db.Column(db.Float)  # 公积金扣除
    tax_deduction = db.Column(db.Float)  # 个税扣除
    other_deduction = db.Column(db.Float)  # 其他扣除
    
    # 总计
    total_salary = db.Column(db.Float, nullable=False)  # 实发工资
    
    # 元数据
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    source_file = db.Column(db.String(255))  # 来源文件
    remarks = db.Column(db.Text)  # 备注
    
    def __repr__(self):
        return f'<SalaryRecord {self.employee.name} - {self.salary_date}>'

class ExcelTemplate(db.Model):
    """Excel模板定义"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 模板名称
    school_type = db.Column(db.String(20), nullable=False)  # 学校类型
    header_rows = db.Column(db.Integer, nullable=False)  # 表头行数
    header_structure = db.Column(db.Text, nullable=False)  # 表头结构（JSON格式）
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 新增字段
    column_mapping = db.Column(db.Text)  # 列映射关系（JSON格式）
    validation_rules = db.Column(db.Text)  # 数据验证规则（JSON格式）
    remarks = db.Column(db.Text)  # 备注说明

    def set_header_structure(self, structure):
        """设置表头结构"""
        self.header_structure = json.dumps(structure, ensure_ascii=False)

    def get_header_structure(self):
        """获取表头结构"""
        return json.loads(self.header_structure) if self.header_structure else {}
        
    def set_column_mapping(self, mapping):
        """设置列映射关系"""
        self.column_mapping = json.dumps(mapping, ensure_ascii=False)
        
    def get_column_mapping(self):
        """获取列映射关系"""
        return json.loads(self.column_mapping) if self.column_mapping else {}
        
    def set_validation_rules(self, rules):
        """设置数据验证规则"""
        self.validation_rules = json.dumps(rules, ensure_ascii=False)
        
    def get_validation_rules(self):
        """获取数据验证规则"""
        return json.loads(self.validation_rules) if self.validation_rules else {}

class SalaryData(db.Model):
    """工资数据（动态结构）"""
    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('excel_template.id'), nullable=False)
    employee_id = db.Column(db.String(50), nullable=False)  # 工号（唯一标识）
    salary_date = db.Column(db.Date, nullable=False)  # 工资月份
    data = db.Column(db.Text, nullable=False)  # 工资数据（JSON格式）
    
    # 新增的重要字段
    sheet_name = db.Column(db.String(100), nullable=False, index=True)  # sheet名称
    school_type = db.Column(db.String(20), nullable=False, index=True)  # 学校类型（中学/小学）
    salary_period = db.Column(db.String(100), nullable=False, index=True)  # 工资周期
    
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 其他字段
    validation_status = db.Column(db.String(20), default='pending')  # 数据验证状态
    validation_message = db.Column(db.Text)  # 验证失败信息
    is_processed = db.Column(db.Boolean, default=False)  # 是否已处理
    process_time = db.Column(db.DateTime)  # 处理时间
    source_file = db.Column(db.String(255))  # 来源文件
    source_row = db.Column(db.Integer)  # 来源行号
    remarks = db.Column(db.Text)  # 备注说明

    template = db.relationship('ExcelTemplate', backref=db.backref('salary_records', lazy=True))

    def set_data_array(self, data_array):
        """设置工资数据（数组格式）"""
        self.data = json.dumps(data_array, ensure_ascii=False)

    def get_data_array(self):
        """获取工资数据（数组格式）"""
        try:
            data = json.loads(self.data) if self.data else []
            # 如果是旧的字典格式，转换为数组格式
            if isinstance(data, dict):
                return list(data.values())
            return data
        except json.JSONDecodeError:
            return []
    
    def get_data_by_headers(self, column_mapping):
        """根据表头映射获取数据字典（用于兼容性）"""
        data_array = self.get_data_array()
        result = {}
        
        for excel_col_idx, col_name in column_mapping.items():
            array_index = list(column_mapping.keys()).index(excel_col_idx)
            if array_index < len(data_array):
                result[col_name] = data_array[array_index]
            else:
                result[col_name] = ''
        
        return result

    # 保留原有方法以兼容性
    def set_data(self, data_dict):
        """设置工资数据（兼容旧格式）"""
        if isinstance(data_dict, list):
            self.set_data_array(data_dict)
        else:
            self.data = json.dumps(data_dict, ensure_ascii=False)

    def get_data(self):
        """获取工资数据（兼容旧格式）"""
        try:
            return json.loads(self.data) if self.data else {}
        except json.JSONDecodeError:
            return {}
        
    def validate(self):
        """验证数据"""
        try:
            rules = self.template.get_validation_rules()
            data = self.get_data()
            errors = []
            
            for field, rule in rules.items():
                if field not in data:
                    if rule.get('required', False):
                        errors.append(f"缺少必填字段：{field}")
                    continue
                    
                value = data[field]
                
                # 类型验证
                if 'type' in rule:
                    if rule['type'] == 'number':
                        try:
                            float(value)
                        except (ValueError, TypeError):
                            errors.append(f"字段 {field} 必须是数字")
                    elif rule['type'] == 'date':
                        try:
                            datetime.strptime(str(value), rule.get('format', '%Y-%m-%d'))
                        except ValueError:
                            errors.append(f"字段 {field} 必须是日期格式")
                
                # 范围验证
                if 'min' in rule and float(value) < rule['min']:
                    errors.append(f"字段 {field} 不能小于 {rule['min']}")
                if 'max' in rule and float(value) > rule['max']:
                    errors.append(f"字段 {field} 不能大于 {rule['max']}")
                
                # 正则验证
                if 'pattern' in rule:
                    import re
                    if not re.match(rule['pattern'], str(value)):
                        errors.append(f"字段 {field} 格式不正确")
            
            if errors:
                self.validation_status = 'failed'
                self.validation_message = '\n'.join(errors)
            else:
                self.validation_status = 'success'
                self.validation_message = None
                
        except Exception as e:
            self.validation_status = 'error'
            self.validation_message = str(e)
            
        return self.validation_status == 'success' 