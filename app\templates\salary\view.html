{% extends "base.html" %}

{% block title %}工资明细{% endblock %}

{% block content %}
<div class="max-w-full mx-auto px-4 py-8">
    <div class="mb-8 flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-800">工资明细</h1>
        <div class="flex space-x-4">
            <a href="{{ url_for('salary.search') }}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                返回搜索
            </a>
            <a href="{{ url_for('salary.export') }}?template_id={{ template.id }}" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors">
                导出Excel
            </a>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-x-auto">
        <table class="min-w-full border-collapse">
            <!-- 动态生成表头 -->
            {% for level in template.get_header_structure()['levels'] %}
            <tr>
                {% for col in level %}
                <th class="border px-4 py-2 bg-gray-50 text-gray-700 font-medium" 
                    {% if col.colspan > 1 %}colspan="{{ col.colspan }}"{% endif %}>
                    {{ col.value }}
                </th>
                {% endfor %}
            </tr>
            {% endfor %}

            <!-- 数据行 -->
            {% for record in records %}
            <tr class="hover:bg-gray-50">
                {% for field in template.get_header_structure()['levels'][-1] %}
                <td class="border px-4 py-2 text-gray-800">
                    {{ record.get_data().get(field.value, '') }}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </table>
    </div>

    {% if not records %}
    <div class="text-center py-8 text-gray-500">
        没有找到工资记录
    </div>
    {% endif %}
</div>

<style>
/* 处理合并单元格的样式 */
{% for merged in template.get_header_structure()['merged_cells'] %}
table tr:nth-child({{ merged.row + 1 }}) th:nth-child({{ merged.col + 1 }}) {
    {% if merged.rowspan > 1 %}
    grid-row: span {{ merged.rowspan }};
    {% endif %}
    {% if merged.colspan > 1 %}
    grid-column: span {{ merged.colspan }};
    {% endif %}
}
{% endfor %}
</style>
{% endblock %} 