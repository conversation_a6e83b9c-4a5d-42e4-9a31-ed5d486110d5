#!/usr/bin/env python3
"""
调试和清理合计记录脚本

使用方法：
python debug_totals.py

功能：
1. 查看数据库中所有可能的合计记录
2. 提供清理选项
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.salary import SalaryData
import logging
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_total_records():
    """查找可能的合计记录"""
    app = create_app()
    
    with app.app_context():
        try:
            total_keywords = ['合计', '总计', '小计', '汇总', '总和', '累计']
            
            print("🔍 查找数据库中的合计记录...")
            
            # 查找employee_id包含合计关键词的记录
            employee_totals = []
            for keyword in total_keywords:
                records = SalaryData.query.filter(SalaryData.employee_id.like(f'%{keyword}%')).all()
                employee_totals.extend(records)
            
            print(f"\n📊 通过employee_id找到 {len(employee_totals)} 条可能的合计记录:")
            for record in employee_totals[:10]:  # 只显示前10条
                print(f"  ID: {record.id}, 姓名: {record.employee_id}, Sheet: {record.sheet_name}, 周期: {record.salary_period}")
            if len(employee_totals) > 10:
                print(f"  ... 还有 {len(employee_totals) - 10} 条记录")
            
            # 查找data字段包含合计关键词的记录
            data_totals = []
            all_records = SalaryData.query.all()
            
            print(f"\n🔍 检查 {len(all_records)} 条记录的data字段...")
            
            for record in all_records:
                try:
                    data = record.get_data()
                    for key, value in data.items():
                        if value and any(keyword in str(value) for keyword in total_keywords):
                            data_totals.append((record, key, value))
                            break
                except:
                    continue
            
            print(f"\n📊 通过data字段找到 {len(data_totals)} 条可能的合计记录:")
            for record, key, value in data_totals[:10]:  # 只显示前10条
                print(f"  ID: {record.id}, 姓名: {record.employee_id}, 字段: {key}, 值: {value}")
            if len(data_totals) > 10:
                print(f"  ... 还有 {len(data_totals) - 10} 条记录")
            
            # 合并去重
            all_total_ids = set()
            for record in employee_totals:
                all_total_ids.add(record.id)
            for record, _, _ in data_totals:
                all_total_ids.add(record.id)
            
            print(f"\n📈 总计发现 {len(all_total_ids)} 条独特的合计记录")
            
            return list(all_total_ids)
            
        except Exception as e:
            logger.error(f"查找合计记录失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return []

def clean_total_records(record_ids):
    """清理合计记录"""
    app = create_app()
    
    with app.app_context():
        try:
            print(f"\n🧹 准备清理 {len(record_ids)} 条合计记录...")
            
            deleted_count = 0
            for record_id in record_ids:
                record = SalaryData.query.get(record_id)
                if record:
                    db.session.delete(record)
                    deleted_count += 1
            
            db.session.commit()
            print(f"✅ 成功清理 {deleted_count} 条合计记录！")
            
        except Exception as e:
            logger.error(f"清理合计记录失败: {str(e)}")
            db.session.rollback()
            return False
    
    return True

def show_sample_data():
    """显示一些示例数据"""
    app = create_app()
    
    with app.app_context():
        records = SalaryData.query.limit(5).all()
        print(f"\n📋 数据库中的示例记录:")
        for record in records:
            data = record.get_data()
            print(f"  ID: {record.id}")
            print(f"  姓名: {record.employee_id}")
            print(f"  Sheet: {record.sheet_name}")
            print(f"  数据示例: {dict(list(data.items())[:3])}")
            print("  ---")

if __name__ == '__main__':
    print("🔧 合计记录调试工具")
    print("=" * 50)
    
    # 显示示例数据
    show_sample_data()
    
    # 查找合计记录
    total_record_ids = find_total_records()
    
    if total_record_ids:
        print(f"\n❗ 发现 {len(total_record_ids)} 条合计记录需要清理")
        response = input("\n是否要清理这些合计记录？(y/N): ")
        
        if response.lower() == 'y':
            success = clean_total_records(total_record_ids)
            if success:
                print("✅ 清理完成！")
            else:
                print("❌ 清理失败！")
        else:
            print("🚫 清理操作已取消")
    else:
        print("✅ 未发现合计记录，数据库是干净的！") 