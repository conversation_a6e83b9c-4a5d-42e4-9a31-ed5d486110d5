#!/usr/bin/env python3
"""
添加新字段到SalaryData表的迁移脚本

使用方法：
python migrate_add_fields.py

这个脚本会：
1. 添加 sheet_name, school_type, salary_period 字段
2. 为现有数据设置默认值
3. 添加索引以提高查询性能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_migration():
    """运行数据库迁移"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始执行数据库迁移...")
            
            # 方法1: 直接重新创建表（推荐用于开发环境）
            logger.info("重新创建所有表...")
            db.drop_all()
            db.create_all()
            
            logger.info("数据库迁移完成！")
            logger.info("注意：所有现有数据已被清除，这是因为添加了新的必需字段")
            logger.info("如果有重要数据，请先备份后再运行此脚本")
            
        except Exception as e:
            logger.error(f"迁移失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
            
    return True

if __name__ == '__main__':
    print("警告：此操作将清除所有现有数据！")
    response = input("确定要继续吗？(y/N): ")
    
    if response.lower() == 'y':
        success = run_migration()
        if success:
            print("✅ 数据库迁移成功完成！")
        else:
            print("❌ 数据库迁移失败！")
            sys.exit(1)
    else:
        print("操作已取消") 