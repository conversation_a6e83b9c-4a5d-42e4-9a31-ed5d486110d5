"""
测试合并单元格检测逻辑
"""

from app import create_app
from app.utils.excel_parser import ExcelParser
import pandas as pd
import numpy as np
import logging

def create_test_data():
    """创建测试用的表头数据"""
    # 模拟一个复杂的嵌套表头
    test_data = [
        ['基本信息', '', '', '工资项目', '', '', '扣除项目', ''],
        ['姓名', '部门', '职位', '基本工资', '绩效奖金', '补贴', '社保', '个税'],
        ['', '', '', '', '', '交通补贴', '', '']
    ]
    
    return pd.DataFrame(test_data)

def test_merged_cells():
    """测试合并单元格检测"""
    app = create_app()
    
    with app.app_context():
        # 设置日志级别为DEBUG
        logging.getLogger('app.utils.excel_parser').setLevel(logging.DEBUG)
        
        print("创建测试Excel解析器...")
        
        # 创建一个临时的解析器对象
        parser = ExcelParser('test.xlsx', 3)  # 假设有3行表头
        
        # 手动设置测试数据
        parser.df = create_test_data()
        
        print("测试数据:")
        print(parser.df)
        print("\n开始分析表头结构...")
        
        # 分析表头结构
        parser.analyze_header_structure()
        
        print("\n分析结果:")
        print(f"层级数: {len(parser.header_structure['levels'])}")
        print(f"合并单元格数: {len(parser.header_structure['merged_cells'])}")
        
        print("\n合并单元格详情:")
        for i, mc in enumerate(parser.header_structure['merged_cells'], 1):
            print(f"{i}. 位置[{mc['row']},{mc['col']}] '{mc['value']}' -> rowspan={mc['rowspan']}, colspan={mc['colspan']}")

if __name__ == '__main__':
    test_merged_cells() 