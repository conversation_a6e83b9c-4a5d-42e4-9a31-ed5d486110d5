"""
迁移脚本：为 sheet_template 表添加 school_type 字段
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.sheet_template import SheetTemplate
from app.models.salary import SalaryData
from sqlalchemy import text

def migrate_sheet_template():
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查是否已经有 school_type 字段
            try:
                db.session.execute(text("SELECT school_type FROM sheet_template LIMIT 1"))
                print("school_type 字段已存在，跳过添加")
            except Exception as e:
                print("添加 school_type 字段...")
                # 添加 school_type 字段
                db.session.execute(text("ALTER TABLE sheet_template ADD COLUMN school_type VARCHAR(20)"))
                db.session.commit()
                print("school_type 字段添加成功")
            
            # 2. 更新现有数据的 school_type
            print("开始迁移现有数据...")
            
            # 获取所有没有 school_type 的模板
            templates_without_school_type = SheetTemplate.query.filter(
                (SheetTemplate.school_type == None) | (SheetTemplate.school_type == '')
            ).all()
            
            print(f"找到 {len(templates_without_school_type)} 个需要更新的模板")
            
            updated_count = 0
            for template in templates_without_school_type:
                # 通过 sheet_name 查找对应的工资数据来确定学校类型
                salary_data = SalaryData.query.filter_by(sheet_name=template.sheet_name).first()
                
                if salary_data:
                    template.school_type = salary_data.school_type
                    print(f"更新模板 '{template.sheet_name}' 的 school_type 为 '{salary_data.school_type}'")
                    updated_count += 1
                else:
                    # 如果找不到对应的工资数据，标记为未知（可以手动处理）
                    template.school_type = 'unknown'
                    print(f"警告：模板 '{template.sheet_name}' 没有找到对应的工资数据，标记为 'unknown'")
            
            # 3. 设置字段为 NOT NULL（如果所有数据都有值的话）
            db.session.commit()
            
            # 检查是否还有 NULL 或空值
            null_count = SheetTemplate.query.filter(
                (SheetTemplate.school_type == None) | 
                (SheetTemplate.school_type == '') | 
                (SheetTemplate.school_type == 'unknown')
            ).count()
            
            if null_count == 0:
                print("设置 school_type 字段为 NOT NULL...")
                db.session.execute(text("UPDATE sheet_template SET school_type = 'high' WHERE school_type IS NULL OR school_type = ''"))
                db.session.commit()
                print("所有数据迁移完成")
            else:
                print(f"警告：还有 {null_count} 条记录的 school_type 需要手动处理")
            
            print(f"成功更新了 {updated_count} 个模板的 school_type")
            
        except Exception as e:
            print(f"迁移失败: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    migrate_sheet_template()
