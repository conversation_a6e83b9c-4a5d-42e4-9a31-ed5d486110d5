import os
from flask import Blueprint, render_template, request, current_app, flash, redirect, url_for, jsonify, send_file
from werkzeug.utils import secure_filename
from app.models.salary import Employee, SalaryRecord, ExcelTemplate, SalaryData
from app.models.sheet_template import SheetTemplate
from app.utils.excel_parser_v2 import ExcelParserV2
from app import db
from sqlalchemy import and_, or_, text
import pandas as pd
from datetime import datetime
import logging
import traceback
import json
from urllib.parse import quote

# 配置日志
logger = logging.getLogger(__name__)

bp = Blueprint('salary', __name__, url_prefix='/salary')

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def check_sheet_name_column_exists():
    """检查sheet_name列是否存在 - SQLite版本"""
    try:
        # 使用SQLite的PRAGMA table_info
        columns = db.session.execute(text("PRAGMA table_info(excel_template)")).fetchall()
        column_names = [col[1] for col in columns]
        return 'sheet_name' in column_names
    except:
        return False

@bp.route('/upload', methods=['GET', 'POST'])
def upload():
    if request.method == 'POST':
        logger.debug("开始处理文件上传请求")  # DEBUG级别日志
        logger.info("接收到文件上传请求")
        logger.debug(f"请求头详情: {dict(request.headers)}")
        logger.debug(f"请求表单数据详情: {dict(request.form)}")
        
        if 'file' not in request.files:
            logger.warning("没有文件被上传")
            logger.debug(f"请求文件: {dict(request.files)}")
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': '没有选择文件'})
            flash('没有选择文件')
            return redirect(request.url)
            
        files = request.files.getlist('file')
        school_type = request.form.get('school_type')
        header_rows = int(request.form.get('header_rows', 4))
        
        logger.info(f"上传参数: school_type={school_type}, header_rows={header_rows}")
        logger.info(f"上传文件数量: {len(files)}")
        logger.debug(f"文件列表: {[f.filename for f in files]}")
        
        if not school_type:
            logger.warning("未选择学校类型")
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': '请选择学校类型'})
            flash('请选择学校类型')
            return redirect(request.url)
            
        upload_folder = current_app.config['HIGH_SCHOOL_FOLDER'] if school_type == 'high' else current_app.config['PRIMARY_SCHOOL_FOLDER']
        logger.debug(f"使用上传目录: {upload_folder}")
        
        # 确保上传目录存在
        if not os.path.exists(upload_folder):
            logger.info(f"创建上传目录: {upload_folder}")
            os.makedirs(upload_folder)
        
        success_files = []
        error_files = []
        
        for file in files:
            if file.filename == '':
                logger.warning("跳过空文件名")
                continue
                
            if file and allowed_file(file.filename):
                # filename = secure_filename(file.filename)
                filename = file.filename
                file_path = os.path.join(upload_folder, filename)
                logger.info(f"处理文件: {filename}")
                
                try:
                    # 保存文件
                    logger.debug(f"保存文件到: {file_path}")
                    file.save(file_path)
                    logger.debug(f"文件大小: {os.path.getsize(file_path)} 字节")
                    
                    # 解析Excel文件
                    logger.info("开始解析Excel文件...")
                    parser = ExcelParserV2(file_path, header_rows)  # 使用新的解析器
                    result = parser.parse()
                    logger.info("Excel文件解析完成")
                    logger.debug(f"解析结果: {result}")
                    
                    if not result or 'sheets' not in result:
                        raise ValueError('文件解析失败')
                    
                    # 处理每个sheet
                    imported_count = 0
                    sheet_name_exists = check_sheet_name_column_exists()
                    logger.info(f"数据库sheet_name字段支持: {sheet_name_exists}")
                    
                    for sheet_name, sheet_data in result['sheets'].items():
                        logger.debug(f"开始处理sheet: {sheet_name}")
                        logger.debug(f"Sheet数据概览: 行数={len(sheet_data.get('data', []))}")
                        
                        if 'error' in sheet_data:
                            logger.error(f"Sheet '{sheet_name}' 解析失败: {sheet_data['error']}")
                            continue
                        
                        # 为每个sheet保存表头结构到新表 - 修改：添加school_type字段
                        if sheet_data['header_structure']:
                            existing_template = SheetTemplate.query.filter_by(
                                sheet_name=sheet_name,
                                template_name='default',
                                school_type=school_type  # 添加school_type过滤条件
                            ).first()
                            
                            if existing_template:
                                # 更新现有模板
                                existing_template.header_structure = json.dumps(
                                    sheet_data['header_structure'], ensure_ascii=False
                                )
                                logger.info(f"更新sheet '{sheet_name}' ({school_type}) 的表头模板")
                            else:
                                # 创建新模板 - 添加school_type字段
                                template = SheetTemplate(
                                    sheet_name=sheet_name,
                                    template_name='default',
                                    school_type=school_type,  # 新增字段
                                    header_structure=json.dumps(
                                        sheet_data['header_structure'], ensure_ascii=False
                                    )
                                )
                                db.session.add(template)
                                logger.info(f"创建sheet '{sheet_name}' ({school_type}) 的表头模板")
                            
                            db.session.flush()  # 获取模板ID
                            template_id = existing_template.id if existing_template else template.id
                        
                        # 处理数据行
                        total_keywords = ['合计', '总计', '小计', '汇总', '总和', '累计']
                        
                        for row_data in sheet_data['data']:
                            # 获取姓名列索引
                            name_col_index = sheet_data.get('name_column_index', 0)
                            
                            # 检查是否有有效的姓名
                            if name_col_index is None or name_col_index >= len(row_data):
                                logger.debug(f"跳过没有姓名列的行")
                                continue
                                
                            name_value = str(row_data[name_col_index]).strip()
                            if not name_value or any(keyword in name_value for keyword in total_keywords):
                                logger.debug(f"跳过无效姓名的行: '{name_value}'")
                                continue
                            
                            # 处理数据格式（数组中的每个元素）
                            processed_row = []
                            for value in row_data:
                                if isinstance(value, (int, float)):
                                    # 数值类型：保留两位小数，四舍五入
                                    processed_row.append(round(float(value), 2))
                                elif isinstance(value, str) and value.strip():
                                    # 尝试转换字符串为数值
                                    try:
                                        float_value = float(value.strip())
                                        processed_row.append(round(float_value, 2))
                                    except ValueError:
                                        # 不是数值，保持原样
                                        processed_row.append(value.strip())
                                else:
                                    processed_row.append(value if value is not None else '')
                            
                            # 使用完整文件名（不含后缀）作为所属周期
                            file_name_without_ext = os.path.splitext(filename)[0]
                            
                            salary_data = SalaryData(
                                template_id=template_id,
                                employee_id=name_value,  # 使用姓名作为employee_id
                                salary_date=datetime.now().date(),
                                sheet_name=sheet_name,
                                school_type=school_type,
                                salary_period=file_name_without_ext.strip()
                            )
                            salary_data.set_data_array(processed_row)  # 使用新方法存储数组
                            db.session.add(salary_data)
                            imported_count += 1
                            logger.debug(f"添加工资数据: 姓名={name_value}, sheet={sheet_name}, 数据长度={len(processed_row)}")
                    
                    db.session.commit()
                    logger.info(f"文件 {filename} 处理成功，总共导入 {imported_count} 条记录")
                    success_files.append(filename)
                    
                except Exception as e:
                    logger.error(f"处理文件 {filename} 时发生错误: {str(e)}")
                    logger.error("错误堆栈跟踪:")
                    logger.error(traceback.format_exc())
                    db.session.rollback()
                    error_files.append({'file': filename, 'error': str(e)})
                finally:
                    # 清理临时文件
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.debug(f"删除临时文件: {file_path}")
            else:
                logger.warning(f"文件 {file.filename} 类型不允许")
                error_files.append({'file': file.filename, 'error': '文件类型不支持'})
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            response_data = {
                'success': len(error_files) == 0,
                'message': '文件上传完成',
                'details': {
                    'success_files': success_files,
                    'error_files': error_files
                }
            }
            logger.info(f"返回上传结果: {response_data}")
            return jsonify(response_data)
            
        for filename in success_files:
            flash(f'文件 {filename} 导入成功')
        for error in error_files:
            flash(f'文件 {error["file"]} 导入失败：{error["error"]}')
            
        return redirect(url_for('salary.upload'))
    
    return render_template('salary/upload.html', now=datetime.now())

@bp.route('/search')
def search():
    # 获取查询参数
    employee_id = request.args.get('employee_id', '')
    sheet_name = request.args.get('sheet_name', '')
    salary_period = request.args.get('salary_period', '')
    
    current_app.logger.debug(f"查询参数: employee_id={employee_id}, sheet_name={sheet_name}, salary_period={salary_period}")
    
    # 构建基础查询
    query = SalaryData.query
    
    # 应用过滤条件
    if employee_id:
        query = query.filter(SalaryData.employee_id.contains(employee_id))
    if sheet_name:
        query = query.filter(SalaryData.sheet_name == sheet_name)
    if salary_period:
        query = query.filter(SalaryData.salary_period == salary_period)
    
    # 获取所有符合条件的记录（用于统计）
    all_records = query.all()
    
    # 按学校类型和sheet分组，并统计总数
    high_school_data = {}
    primary_school_data = {}
    
    for record in all_records:
        school_type = record.school_type
        record_sheet_name = record.sheet_name
        
        if school_type == 'high':
            if record_sheet_name not in high_school_data:
                high_school_data[record_sheet_name] = {
                    'total_count': 0,  # 总记录数
                    'records': [],     # 当前页记录（默认空，通过AJAX加载）
                    'header_structure': None
                }
            high_school_data[record_sheet_name]['total_count'] += 1
        elif school_type == 'primary':
            if record_sheet_name not in primary_school_data:
                primary_school_data[record_sheet_name] = {
                    'total_count': 0,  # 总记录数
                    'records': [],     # 当前页记录（默认空，通过AJAX加载）
                    'header_structure': None
                }
            primary_school_data[record_sheet_name]['total_count'] += 1
    
    # 如果有sheet筛选条件，只保留匹配的sheet
    if sheet_name:
        high_school_data = {k: v for k, v in high_school_data.items() if k == sheet_name}
        primary_school_data = {k: v for k, v in primary_school_data.items() if k == sheet_name}
    
    # 获取表头结构
    for sheet_name_key in high_school_data.keys():
        template = SheetTemplate.query.filter_by(sheet_name=sheet_name_key).first()
        if template:
            high_school_data[sheet_name_key]['header_structure'] = template.header_structure
    
    for sheet_name_key in primary_school_data.keys():
        template = SheetTemplate.query.filter_by(sheet_name=sheet_name_key).first()
        if template:
            primary_school_data[sheet_name_key]['header_structure'] = template.header_structure
    
    # 获取所有可选项（不受当前筛选条件影响）
    available_sheets = db.session.query(SalaryData.sheet_name).distinct().all()
    available_sheets = [sheet[0] for sheet in available_sheets]
    
    available_periods = db.session.query(SalaryData.salary_period).distinct().all()
    available_periods = [period[0] for period in available_periods]
    
    # 统计总数（基于筛选后的结果）
    total_high = sum(sheet_data['total_count'] for sheet_data in high_school_data.values())
    total_primary = sum(sheet_data['total_count'] for sheet_data in primary_school_data.values())
    
    return render_template('salary/search.html', 
                         high_school_data=high_school_data,
                         primary_school_data=primary_school_data,
                         total_high=total_high,
                         total_primary=total_primary,
                         available_sheets=available_sheets,
                         available_periods=available_periods,
                         current_params=request.args,
                         now=datetime.now())

def load_sheet_data(sheet_name, school_type, employee_id='', search_sheet_name='', salary_period='', page=1, per_page=50):
    """加载指定sheet的分页数据"""
    query = SalaryData.query.filter_by(sheet_name=sheet_name, school_type=school_type)
    
    # 应用搜索条件 - 支持多值
    if employee_id:
        if ',' in employee_id:
            # 多个姓名
            employee_list = [name.strip() for name in employee_id.split(',')]
            query = query.filter(SalaryData.employee_id.in_(employee_list))
        else:
            # 单个姓名
            query = query.filter(SalaryData.employee_id.contains(employee_id))
    
    if search_sheet_name:
        if ',' in search_sheet_name:
            # 多个Sheet
            sheet_list = [sheet.strip() for sheet in search_sheet_name.split(',')]
            query = query.filter(SalaryData.sheet_name.in_(sheet_list))
        else:
            # 单个Sheet
            query = query.filter(SalaryData.sheet_name == search_sheet_name)
    
    if salary_period:
        if ',' in salary_period:
            # 多个周期
            period_list = [period.strip() for period in salary_period.split(',')]
            query = query.filter(SalaryData.salary_period.in_(period_list))
        else:
            # 单个周期
            query = query.filter(SalaryData.salary_period == salary_period)
    
    # 分页
    pagination = query.paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    # 获取表头结构 - 修改：添加school_type过滤条件
    template = SheetTemplate.query.filter_by(
        sheet_name=sheet_name,
        school_type=school_type  # 添加school_type过滤条件
    ).first()
    header_structure = template.header_structure if template else None
    
    return {
        'records': pagination.items,
        'pagination': pagination,
        'header_structure': header_structure
    }

@bp.route('/api/sheet_data')
def api_sheet_data():
    """AJAX API - 获取指定sheet的分页数据"""
    sheet_name = request.args.get('sheet_name', '')
    school_type = request.args.get('school_type', '')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 50))
    
    # 搜索条件
    employee_id = request.args.get('employee_id', '')
    search_sheet_name = request.args.get('search_sheet_name', '')
    salary_period = request.args.get('salary_period', '')
    
    current_app.logger.debug(f"AJAX请求: sheet_name={sheet_name}, school_type={school_type}, page={page}")
    
    if not sheet_name or not school_type:
        return {'error': '缺少必要参数'}, 400
    
    try:
        sheet_data = load_sheet_data(sheet_name, school_type, employee_id, search_sheet_name, salary_period, page, per_page)
        
        # 转换数据为JSON格式
        records_data = []
        for record in sheet_data['records']:
            record_data = {
                'id': record.id,
                'employee_id': record.employee_id,
                'salary_period': record.salary_period,
                'data_array': record.get_data_array()
            }
            records_data.append(record_data)
        
        pagination_data = {
            'page': sheet_data['pagination'].page,
            'pages': sheet_data['pagination'].pages,
            'per_page': sheet_data['pagination'].per_page,
            'total': sheet_data['pagination'].total,
            'has_prev': sheet_data['pagination'].has_prev,
            'has_next': sheet_data['pagination'].has_next,
            'prev_num': sheet_data['pagination'].prev_num,
            'next_num': sheet_data['pagination'].next_num
        }
        
        # 确保表头结构正确传递 - 已经是字符串形式
        header_structure = sheet_data['header_structure']
        current_app.logger.debug(f"返回的表头结构类型: {type(header_structure)}")
        if header_structure:
            current_app.logger.debug(f"表头结构内容预览: {header_structure[:200] if len(str(header_structure)) > 200 else header_structure}")
        
        return {
            'records': records_data,
            'pagination': pagination_data,
            'header_structure': header_structure  # 保持为JSON字符串，前端负责解析
        }
        
    except Exception as e:
        current_app.logger.error(f"AJAX请求错误: {e}")
        return {'error': str(e)}, 500

@bp.route('/export')
def export():
    template_id = request.args.get('template_id')
    if not template_id:
        flash('请选择要导出的模板')
        return redirect(url_for('salary.search'))
    
    template = SheetTemplate.query.get_or_404(template_id)
    
    # 获取查询参数
    employee_id = request.args.get('employee_id', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    
    # 构建查询
    query = SalaryData.query.filter(SalaryData.template_id == template_id)
    
    if employee_id:
        query = query.filter(SalaryData.employee_id.like(f'%{employee_id}%'))
    if start_date:
        query = query.filter(SalaryData.salary_date >= datetime.strptime(start_date, '%Y-%m-%d').date())
    if end_date:
        query = query.filter(SalaryData.salary_date <= datetime.strptime(end_date, '%Y-%m-%d').date())
    
    # 执行查询
    records = query.order_by(SalaryData.salary_date.desc()).all()
    
    # 准备数据
    data = []
    header_structure = template.get_header_structure()
    columns = [col['value'] for col in header_structure['levels'][-1]]  # 使用最后一层表头作为列名
    
    for record in records:
        row_data = record.get_data()
        data.append([row_data.get(col, '') for col in columns])
    
    # 创建DataFrame
    df = pd.DataFrame(data, columns=columns)
    
    # 生成Excel文件
    output_file = os.path.join(current_app.instance_path, 'temp_export.xlsx')
    
    # 使用ExcelWriter以支持复杂表头
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='工资数据')
        
        # 获取工作表
        worksheet = writer.sheets['工资数据']
        
        # 应用合并单元格
        for merged in header_structure['merged_cells']:
            worksheet.merge_cells(
                start_row=merged['row'] + 1,
                start_column=merged['col'] + 1,
                end_row=merged['row'] + merged['rowspan'],
                end_column=merged['col'] + merged['colspan']
            )
    
    return send_file(
        output_file,
        as_attachment=True,
        download_name=f'salary_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    ) 

# @bp.route('/detail/<int:record_id>')
# def detail(record_id):
#     """获取工资记录详情"""
#     # ... 删除这个函数 

# 添加新的API接口获取所有可选项
@bp.route('/api/options')
def api_options():
    """获取所有可选项"""
    try:
        # 获取所有姓名
        employees = db.session.query(SalaryData.employee_id).distinct().all()
        employee_list = [emp[0] for emp in employees if emp[0]]
        
        # 获取所有sheet，并标注学校类型
        sheets = db.session.query(SalaryData.sheet_name, SalaryData.school_type).distinct().all()
        sheet_list = []
        for sheet_name, school_type in sheets:
            if sheet_name:
                sheet_list.append({
                    'name': sheet_name,
                    'school_type': school_type,
                    'display': f"{sheet_name} ({'中学' if school_type == 'high' else '小学'})"
                })
        
        # 获取所有工资周期
        periods = db.session.query(SalaryData.salary_period).distinct().all()
        period_list = [period[0] for period in periods if period[0]]
        
        return {
            'employees': sorted(employee_list),
            'sheets': sorted(sheet_list, key=lambda x: x['display']),
            'periods': sorted(period_list)
        }
    except Exception as e:
        current_app.logger.error(f"获取选项数据错误: {e}")
        return {'error': str(e)}, 500

@bp.route('/api/search')
def api_search():
    """AJAX搜索接口 - 只返回有匹配数据的sheet"""
    try:
        # 获取查询参数
        employee_id = request.args.get('employee_id', '').strip()
        search_sheet_name = request.args.get('search_sheet_name', '').strip()
        salary_period = request.args.get('salary_period', '').strip()
        
        current_app.logger.info(f"API搜索参数 - 姓名: {employee_id}, Sheet: {search_sheet_name}, 周期: {salary_period}")
        
        # 构建基础查询
        query = SalaryData.query
        
        # 添加查询条件
        if employee_id:
            employee_names = [name.strip() for name in employee_id.split(',') if name.strip()]
            if employee_names:
                query = query.filter(SalaryData.employee_id.in_(employee_names))
        
        if search_sheet_name:
            sheet_names = [name.strip() for name in search_sheet_name.split(',') if name.strip()]
            if sheet_names:
                query = query.filter(SalaryData.sheet_name.in_(sheet_names))
        
        if salary_period:
            periods = [period.strip() for period in salary_period.split(',') if period.strip()]
            if periods:
                query = query.filter(SalaryData.salary_period.in_(periods))
        
        # 获取所有匹配的记录
        all_records = query.all()
        current_app.logger.info(f"匹配的总记录数: {len(all_records)}")
        
        # 按学校类型和sheet分组
        high_school_data = {}
        primary_school_data = {}
        
        for record in all_records:
            sheet_name = record.sheet_name
            school_type = record.school_type
            
            if school_type == 'high':
                if sheet_name not in high_school_data:
                    high_school_data[sheet_name] = {
                        'total_count': 0,
                        'header_structure': None
                    }
                high_school_data[sheet_name]['total_count'] += 1
                
                # 获取表头结构（只需要一次）
                if not high_school_data[sheet_name]['header_structure']:
                    template = SheetTemplate.query.filter_by(
                        sheet_name=sheet_name
                    ).first()
                    if template:
                        high_school_data[sheet_name]['header_structure'] = template.header_structure
            
            elif school_type == 'primary':
                if sheet_name not in primary_school_data:
                    primary_school_data[sheet_name] = {
                        'total_count': 0,
                        'header_structure': None
                    }
                primary_school_data[sheet_name]['total_count'] += 1
                
                # 获取表头结构（只需要一次）
                if not primary_school_data[sheet_name]['header_structure']:
                    template = SheetTemplate.query.filter_by(
                        sheet_name=sheet_name
                    ).first()
                    if template:
                        primary_school_data[sheet_name]['header_structure'] = template.header_structure
        
        # 计算总数（匹配查询条件的记录总数）
        total_high = sum(sheet_data['total_count'] for sheet_data in high_school_data.values())
        total_primary = sum(sheet_data['total_count'] for sheet_data in primary_school_data.values())
        
        current_app.logger.info(f"搜索结果 - 中学记录: {total_high}, 小学记录: {total_primary}")
        current_app.logger.info(f"中学sheet数: {len(high_school_data)}, 小学sheet数: {len(primary_school_data)}")
        
        return jsonify({
            'success': True,
            'high_school_data': high_school_data,
            'primary_school_data': primary_school_data,
            'total_high': total_high,
            'total_primary': total_primary
        })
        
    except Exception as e:
        current_app.logger.error(f"API搜索失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'搜索失败: {str(e)}'
        })

# 添加新的API接口获取按学校类型分组的工资周期
@bp.route('/api/periods_by_school_type')
def api_periods_by_school_type():
    """获取按学校类型分组的工资周期"""
    try:
        # 获取中学的工资周期
        high_periods = db.session.query(SalaryData.salary_period).filter_by(school_type='high').distinct().all()
        high_period_list = [period[0] for period in high_periods if period[0]]
        
        # 获取小学的工资周期
        primary_periods = db.session.query(SalaryData.salary_period).filter_by(school_type='primary').distinct().all()
        primary_period_list = [period[0] for period in primary_periods if period[0]]
        
        return {
            'high': sorted(high_period_list),
            'primary': sorted(primary_period_list)
        }
    except Exception as e:
        current_app.logger.error(f"获取分组周期数据错误: {e}")
        return {'error': str(e)}, 500

@bp.route('/api/export')
def api_export():
    """导出Excel接口"""
    try:
        from flask import current_app
        import tempfile
        import os
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
        from openpyxl.utils import get_column_letter
        import json
        
        current_app.logger.info("开始导出Excel数据")
        
        # 获取查询参数（与search API相同的逻辑）
        employee_id = request.args.get('employee_id', '').strip()
        search_sheet_name = request.args.get('search_sheet_name', '').strip()
        salary_period = request.args.get('salary_period', '').strip()
        
        current_app.logger.info(f"导出参数: employee_id={employee_id}, sheet_name={search_sheet_name}, period={salary_period}")
        
        # 构建基础查询 - 不要join SheetTemplate，因为没有直接关系
        query = SalaryData.query
        
        # 应用过滤条件
        if employee_id:
            names = [name.strip() for name in employee_id.split(',') if name.strip()]
            if names:
                query = query.filter(SalaryData.employee_id.in_(names))
        
        if search_sheet_name:
            sheet_names = [name.strip() for name in search_sheet_name.split(',') if name.strip()]
            if sheet_names:
                query = query.filter(SalaryData.sheet_name.in_(sheet_names))
        
        if salary_period:
            periods = [period.strip() for period in salary_period.split(',') if period.strip()]
            if periods:
                query = query.filter(SalaryData.salary_period.in_(periods))
        
        # 按学校类型分组获取所有数据
        all_records = query.all()
        current_app.logger.info(f"查询到总记录数: {len(all_records)}")
        
        # 按学校类型和sheet名称分组
        grouped_data = {'high': {}, 'primary': {}}
        
        for record in all_records:
            school_type = record.school_type
            sheet_name = record.sheet_name
            
            if school_type not in grouped_data:
                continue
                
            if sheet_name not in grouped_data[school_type]:
                # 通过sheet_name和school_type查找表头结构 - 修改：添加school_type过滤条件
                template = SheetTemplate.query.filter_by(
                    sheet_name=sheet_name,
                    school_type=school_type  # 添加school_type过滤条件
                ).first()
                
                grouped_data[school_type][sheet_name] = {
                    'records': [],
                    'header_structure': template.get_header_structure() if template else None
                }
            
            grouped_data[school_type][sheet_name]['records'].append(record)
        
        current_app.logger.info(f"中学数据: {len(grouped_data['high'])} 个sheet")
        current_app.logger.info(f"小学数据: {len(grouped_data['primary'])} 个sheet")
        
        # 生成Excel文件
        excel_files = {}
        
        # 生成中学Excel
        if grouped_data['high']:
            excel_files['中学工资数据.xlsx'] = generate_excel_file(grouped_data['high'], '中学')
        
        # 生成小学Excel  
        if grouped_data['primary']:
            excel_files['小学工资数据.xlsx'] = generate_excel_file(grouped_data['primary'], '小学')
        
        if not excel_files:
            return jsonify({'error': '没有数据可导出'}), 400
        
        # 如果只有一个文件，直接返回文件
        if len(excel_files) == 1:
            filename, file_content = next(iter(excel_files.items()))
            
            from flask import make_response
            response = make_response(file_content)
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            # 使用RFC 5987标准处理中文文件名
            encoded_filename = quote(filename.encode('utf-8'))
            response.headers['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'\'{encoded_filename}'
            return response
        
        # 如果有多个文件，打包成ZIP
        import zipfile
        import io
        
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for filename, content in excel_files.items():
                zip_file.writestr(filename, content)
        
        zip_buffer.seek(0)
        
        from flask import make_response
        response = make_response(zip_buffer.getvalue())
        response.headers['Content-Type'] = 'application/zip'
        # 使用RFC 5987标准处理中文文件名
        zip_filename = '工资数据导出.zip'
        encoded_zip_filename = quote(zip_filename.encode('utf-8'))
        response.headers['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'\'{encoded_zip_filename}'
        return response
        
    except Exception as e:
        current_app.logger.error(f"导出Excel失败: {str(e)}")
        current_app.logger.error("错误堆栈:")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({'error': f'导出失败: {str(e)}'}), 500

def generate_excel_file(sheet_data, school_type):
    """生成Excel文件"""
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter
    from openpyxl.cell import MergedCell
    import json
    import io
    
    wb = Workbook()
    # 删除默认的工作表
    wb.remove(wb.active)
    
    # 定义样式
    header_font = Font(bold=True, size=11)
    header_fill = PatternFill(start_color="E6E6FA", end_color="E6E6FA", fill_type="solid")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center')
    
    for sheet_name, sheet_info in sheet_data.items():
        # 创建工作表
        ws = wb.create_sheet(title=sheet_name[:31])  # Excel sheet名称限制31字符
        
        records = sheet_info['records']
        header_structure = sheet_info['header_structure']
        
        if not records:
            continue
        
        # 找到姓名列的位置
        name_column_index = find_name_column_index(records, header_structure)
        current_app.logger.info(f"Sheet '{sheet_name}' 的姓名列位置: {name_column_index}")
        
        # 写入表头
        current_row = 1
        max_col = 0
        
        if header_structure and 'levels' in header_structure:
            # 渲染复杂表头
            levels = header_structure['levels']
            merged_cells = header_structure.get('merged_cells', [])
            
            # 创建合并单元格映射 - 修复变量名冲突
            covered_cells = set()
            for mc in merged_cells:
                for row_offset in range(mc['row'], mc['row'] + mc['rowspan']):
                    for col_offset in range(mc['col'], mc['col'] + mc['colspan']):
                        if row_offset != mc['row'] or col_offset != mc['col']:
                            covered_cells.add(f"{row_offset},{col_offset}")
            
            # 写入表头数据
            for level_idx, level in enumerate(levels):
                for col_idx, cell in enumerate(level):
                    cell_key = f"{level_idx},{col_idx}"
                    
                    # 跳过被覆盖的单元格
                    if cell_key in covered_cells:
                        continue
                    
                    excel_row = level_idx + 1
                    excel_col = col_idx + 1
                    
                    # 写入单元格值
                    cell_obj = ws.cell(row=excel_row, column=excel_col, value=cell.get('value', ''))
                    cell_obj.font = header_font
                    cell_obj.fill = header_fill
                    cell_obj.border = border
                    cell_obj.alignment = center_alignment
                    
                    # 查找合并信息
                    merge_info = None
                    for mc in merged_cells:
                        if mc['row'] == level_idx and mc['col'] == col_idx:
                            merge_info = mc
                            break
                    
                    # 应用合并
                    if merge_info and (merge_info['rowspan'] > 1 or merge_info['colspan'] > 1):
                        start_row = excel_row
                        start_col = excel_col
                        end_row = start_row + merge_info['rowspan'] - 1
                        end_col = start_col + merge_info['colspan'] - 1
                        
                        ws.merge_cells(
                            start_row=start_row, start_column=start_col,
                            end_row=end_row, end_column=end_col
                        )
                    
                    max_col = max(max_col, excel_col)
            
            current_row = len(levels) + 1
            
            # 添加工资周期列表头
            period_col = max_col + 1
            if len(levels) > 0:
                # 合并所有表头行
                ws.merge_cells(start_row=1, start_column=period_col, end_row=len(levels), end_column=period_col)
            
            period_cell = ws.cell(row=1, column=period_col, value='工资周期')
            period_cell.font = header_font
            period_cell.fill = header_fill
            period_cell.border = border
            period_cell.alignment = center_alignment
            
            max_col = period_col
            
        else:
            # 简单表头
            data_array = records[0].get_data_array()
            for col_idx in range(len(data_array)):
                cell_obj = ws.cell(row=1, column=col_idx + 1, value=f'列{col_idx + 1}')
                cell_obj.font = header_font
                cell_obj.fill = header_fill
                cell_obj.border = border
                cell_obj.alignment = center_alignment
            
            # 工资周期列
            period_cell = ws.cell(row=1, column=len(data_array) + 1, value='工资周期')
            period_cell.font = header_font
            period_cell.fill = header_fill
            period_cell.border = border
            period_cell.alignment = center_alignment
            
            max_col = len(data_array) + 1
            current_row = 2
        
        # 写入数据行 - 修改合计逻辑，只对姓名列之后的数字列进行合计
        data_col_count = max_col - 1  # 排除工资周期列
        sum_values = [0] * data_col_count
        sum_counts = [0] * data_col_count
        
        for record in records:
            data_array = record.get_data_array()

            # 写入数据
            for col_idx, value in enumerate(data_array):
                if col_idx + 1 <= data_col_count:
                    # 检查是否是合并单元格
                    cell_obj = ws.cell(row=current_row, column=col_idx + 1)

                    # 如果是合并单元格，跳过写入（只能写入合并区域的左上角）
                    if isinstance(cell_obj, MergedCell):
                        current_app.logger.debug(f"跳过合并单元格 ({current_row}, {col_idx + 1})")
                        continue

                    # 安全写入数据
                    cell_obj.value = value
                    cell_obj.border = border
                    
                    # 只对姓名列之后的数字列进行累加
                    if name_column_index is not None and col_idx > name_column_index and is_numeric(value):
                        try:
                            num_value = float(value)
                            sum_values[col_idx] += num_value
                            sum_counts[col_idx] += 1
                        except (ValueError, TypeError):
                            pass
            
            # 写入工资周期
            period_cell = ws.cell(row=current_row, column=max_col, value=record.salary_period)
            period_cell.border = border
            
            current_row += 1
        
        # 添加合计行 - 修改合计行逻辑，确保列对齐正确
        if current_row > (len(header_structure.get('levels', [])) + 1 if header_structure else 2):
            # 合计标签 - 放在第一列
            total_cell = ws.cell(row=current_row, column=1, value='合计')
            total_cell.font = Font(bold=True, size=11)
            total_cell.fill = PatternFill(start_color="FFE4E1", end_color="FFE4E1", fill_type="solid")
            total_cell.border = border
            total_cell.alignment = center_alignment
            
            # 为所有数据列填充合计行
            for col_idx in range(data_col_count):
                excel_col = col_idx + 1
                
                if name_column_index is not None and col_idx > name_column_index and sum_counts[col_idx] > 0:
                    # 姓名列之后的数字列显示合计
                    total_value = sum_values[col_idx]
                    if total_value == int(total_value):
                        display_value = int(total_value)
                    else:
                        display_value = round(total_value, 2)
                    
                    total_data_cell = ws.cell(row=current_row, column=excel_col, value=display_value)
                else:
                    # 姓名列及之前的列，或非数字列留空
                    total_data_cell = ws.cell(row=current_row, column=excel_col, value='')
                
                total_data_cell.font = Font(bold=True, size=11)
                total_data_cell.fill = PatternFill(start_color="FFE4E1", end_color="FFE4E1", fill_type="solid")
                total_data_cell.border = border
                total_data_cell.alignment = center_alignment
            
            # 合计行的工资周期列
            total_period_cell = ws.cell(row=current_row, column=max_col, value='')
            total_period_cell.font = Font(bold=True, size=11)
            total_period_cell.fill = PatternFill(start_color="FFE4E1", end_color="FFE4E1", fill_type="solid")
            total_period_cell.border = border
        
        # 调整列宽
        for col in range(1, max_col + 1):
            ws.column_dimensions[get_column_letter(col)].width = 15
    
    # 保存到内存
    excel_buffer = io.BytesIO()
    wb.save(excel_buffer)
    excel_buffer.seek(0)
    
    return excel_buffer.getvalue()

def find_name_column_index(records, header_structure):
    """查找姓名列的索引位置"""
    if not records:
        return None
    
    # 方法1: 从表头结构中查找包含"姓名"、"名字"等关键词的列
    if header_structure and 'levels' in header_structure:
        name_keywords = ['姓名', '名字', '员工姓名', '人员', '员工', '名称']
        
        # 检查最后一层表头（通常是实际的列标题）
        last_level = header_structure['levels'][-1] if header_structure['levels'] else []
        
        for col_idx, cell in enumerate(last_level):
            cell_value = cell.get('value', '').strip()
            if any(keyword in cell_value for keyword in name_keywords):
                current_app.logger.info(f"从表头找到姓名列: 位置={col_idx}, 标题='{cell_value}'")
                return col_idx
        
        # 如果最后一层没找到，检查所有层
        for level in header_structure['levels']:
            for col_idx, cell in enumerate(level):
                cell_value = cell.get('value', '').strip()
                if any(keyword in cell_value for keyword in name_keywords):
                    current_app.logger.info(f"从表头其他层找到姓名列: 位置={col_idx}, 标题='{cell_value}'")
                    return col_idx
    
    # 方法2: 从数据中推断（查找第一个看起来像姓名的列）
    # 取前几行数据进行分析
    sample_records = records[:min(10, len(records))]
    
    for col_idx in range(len(sample_records[0].get_data_array())):
        is_name_column = True
        
        for record in sample_records:
            data_array = record.get_data_array()
            if col_idx >= len(data_array):
                is_name_column = False
                break
            
            value = str(data_array[col_idx]).strip()
            
            # 检查是否像姓名：
            # 1. 不是纯数字
            # 2. 长度在合理范围内（1-10个字符）
            # 3. 不包含特殊符号（除了常见的中文姓名字符）
            if (not value or 
                value.replace('.', '').replace('-', '').isdigit() or  # 是数字
                len(value) > 10 or  # 太长
                len(value) < 1 or   # 太短
                any(char in value for char in ['=', '+', '*', '/', '%', '$', '￥'])):  # 包含计算符号
                is_name_column = False
                break
        
        if is_name_column:
            current_app.logger.info(f"从数据推断姓名列位置: {col_idx}")
            return col_idx
    
    # 方法3: 默认假设第一列是姓名列
    current_app.logger.warning("无法确定姓名列位置，默认使用第0列")
    return 0

def is_numeric(value):
    """检查值是否为数字"""
    if value is None or value == '':
        return False
    try:
        float(value)
        return True
    except (ValueError, TypeError):
        return False

@bp.route('/api/clear_data', methods=['POST'])
def api_clear_data():
    """清空数据API接口"""
    try:
        from flask import request, current_app, jsonify
        import json
        
        # 检查请求是否为AJAX
        if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'error': '无效的请求'}), 400
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '缺少请求数据'}), 400
        
        clear_high = data.get('clear_high', False)
        clear_primary = data.get('clear_primary', False)
        
        if not clear_high and not clear_primary:
            return jsonify({'success': False, 'error': '请至少选择一种数据类型'}), 400
        
        current_app.logger.info(f"开始清空数据 - 中学: {clear_high}, 小学: {clear_primary}")
        
        # 统计即将删除的数据
        deleted_counts = {'high': {'salary': 0, 'template': 0}, 'primary': {'salary': 0, 'template': 0}}
        
        try:
            # 清空中学数据
            if clear_high:
                # 统计中学工资数据
                high_salary_count = SalaryData.query.filter_by(school_type='high').count()
                deleted_counts['high']['salary'] = high_salary_count
                
                # 删除中学工资数据
                SalaryData.query.filter_by(school_type='high').delete()
                current_app.logger.info(f"删除中学工资数据: {high_salary_count} 条")
                
                # 删除中学Sheet模板 - 修改：直接通过school_type删除
                high_template_count = SheetTemplate.query.filter_by(school_type='high').count()
                deleted_high_templates = SheetTemplate.query.filter_by(school_type='high').delete()
                deleted_counts['high']['template'] = deleted_high_templates
                current_app.logger.info(f"删除中学模板数据: {deleted_high_templates} 条")
            
            # 清空小学数据
            if clear_primary:
                # 统计小学工资数据
                primary_salary_count = SalaryData.query.filter_by(school_type='primary').count()
                deleted_counts['primary']['salary'] = primary_salary_count
                
                # 删除小学工资数据
                SalaryData.query.filter_by(school_type='primary').delete()
                current_app.logger.info(f"删除小学工资数据: {primary_salary_count} 条")
                
                # 删除小学Sheet模板 - 修改：直接通过school_type删除
                primary_template_count = SheetTemplate.query.filter_by(school_type='primary').count()
                deleted_primary_templates = SheetTemplate.query.filter_by(school_type='primary').delete()
                deleted_counts['primary']['template'] = deleted_primary_templates
                current_app.logger.info(f"删除小学模板数据: {deleted_primary_templates} 条")
            
            # 提交事务
            db.session.commit()
            
            # 构建成功消息
            message_parts = []
            total_deleted = 0
            
            if clear_high:
                high_total = deleted_counts['high']['salary'] + deleted_counts['high']['template']
                message_parts.append(f"中学数据 {high_total} 条（工资数据 {deleted_counts['high']['salary']} 条，模板 {deleted_counts['high']['template']} 条）")
                total_deleted += high_total
            
            if clear_primary:
                primary_total = deleted_counts['primary']['salary'] + deleted_counts['primary']['template']
                message_parts.append(f"小学数据 {primary_total} 条（工资数据 {deleted_counts['primary']['salary']} 条，模板 {deleted_counts['primary']['template']} 条）")
                total_deleted += primary_total
            
            success_message = f"成功清空 {', '.join(message_parts)}，共计 {total_deleted} 条记录"
            
            current_app.logger.info(f"数据清空完成: {success_message}")
            
            return jsonify({
                'success': True,
                'message': success_message,
                'details': deleted_counts
            })
            
        except Exception as e:
            # 回滚事务
            db.session.rollback()
            raise e
            
    except Exception as e:
        current_app.logger.error(f"清空数据失败: {str(e)}")
        current_app.logger.error("错误堆栈:")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'清空数据失败: {str(e)}'
        }), 500