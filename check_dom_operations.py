#!/usr/bin/env python3
"""
DOM操作安全检查脚本
用于扫描HTML模板文件中的不安全DOM操作
"""

import os
import re
from pathlib import Path

def scan_file(file_path):
    """扫描单个文件中的DOM操作"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"无法读取文件 {file_path}: {e}")
        return []

    issues = []
    lines = content.split('\n')
    
    # 定义需要检查的模式
    patterns = [
        {
            'pattern': r'document\.getElementById\([\'"]([^\'"]+)[\'"]\)',
            'description': '直接使用 document.getElementById',
            'suggestion': '使用 DOMUtils.safeGetElement'
        },
        {
            'pattern': r'document\.querySelector\([\'"]([^\'"]+)[\'"]\)',
            'description': '直接使用 document.querySelector',
            'suggestion': '使用 DOMUtils.safeGetElement 或添加空值检查'
        },
        {
            'pattern': r'\.innerHTML\s*=',
            'description': '直接设置 innerHTML',
            'suggestion': '使用 DOMUtils.safeSetHTML'
        },
        {
            'pattern': r'\.classList\.(add|remove|toggle)',
            'description': '直接操作 classList',
            'suggestion': '使用 DOMUtils.safeToggleClass'
        },
        {
            'pattern': r'\.addEventListener\(',
            'description': '直接添加事件监听器',
            'suggestion': '使用 DOMUtils.safeAddEventListener'
        }
    ]
    
    for line_num, line in enumerate(lines, 1):
        for pattern_info in patterns:
            matches = re.finditer(pattern_info['pattern'], line)
            for match in matches:
                issues.append({
                    'file': file_path,
                    'line': line_num,
                    'content': line.strip(),
                    'description': pattern_info['description'],
                    'suggestion': pattern_info['suggestion'],
                    'match': match.group(0)
                })
    
    return issues

def scan_templates_directory(templates_dir):
    """扫描模板目录"""
    templates_path = Path(templates_dir)
    if not templates_path.exists():
        print(f"模板目录不存在: {templates_dir}")
        return []
    
    all_issues = []
    
    # 扫描所有HTML文件
    for html_file in templates_path.rglob('*.html'):
        issues = scan_file(html_file)
        all_issues.extend(issues)
    
    return all_issues

def generate_report(issues):
    """生成报告"""
    if not issues:
        print("✅ 没有发现需要重构的DOM操作！")
        return
    
    print(f"🔍 发现 {len(issues)} 个需要重构的DOM操作：\n")
    
    # 按文件分组
    files_issues = {}
    for issue in issues:
        file_path = issue['file']
        if file_path not in files_issues:
            files_issues[file_path] = []
        files_issues[file_path].append(issue)
    
    # 输出报告
    for file_path, file_issues in files_issues.items():
        print(f"📄 文件: {file_path}")
        print(f"   发现 {len(file_issues)} 个问题：")
        
        for issue in file_issues:
            print(f"   ⚠️  第 {issue['line']} 行: {issue['description']}")
            print(f"      代码: {issue['content']}")
            print(f"      建议: {issue['suggestion']}")
            print()
        
        print("-" * 80)
    
    # 统计信息
    print("\n📊 统计信息:")
    pattern_counts = {}
    for issue in issues:
        desc = issue['description']
        pattern_counts[desc] = pattern_counts.get(desc, 0) + 1
    
    for pattern, count in sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"   {pattern}: {count} 次")

def main():
    """主函数"""
    print("🔍 DOM操作安全检查工具")
    print("=" * 50)
    
    # 检查模板目录
    templates_dir = "app/templates"
    if not os.path.exists(templates_dir):
        print(f"❌ 模板目录不存在: {templates_dir}")
        print("请在项目根目录运行此脚本")
        return
    
    print(f"📂 扫描目录: {templates_dir}")
    
    # 扫描文件
    issues = scan_templates_directory(templates_dir)
    
    # 生成报告
    generate_report(issues)
    
    if issues:
        print(f"\n💡 建议:")
        print("1. 参考 DOM_SAFETY_GUIDE.md 进行重构")
        print("2. 优先重构用户交互频繁的功能")
        print("3. 一个文件一个文件地进行重构")
        print("4. 重构后测试功能是否正常")

if __name__ == "__main__":
    main()
