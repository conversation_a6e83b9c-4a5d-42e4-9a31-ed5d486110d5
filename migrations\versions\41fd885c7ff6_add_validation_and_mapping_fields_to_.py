"""Add validation and mapping fields to ExcelTemplate and SalaryData

Revision ID: 41fd885c7ff6
Revises: 0a41172c3cfd
Create Date: 2025-05-21 20:12:54.930744

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '41fd885c7ff6'
down_revision = '0a41172c3cfd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('excel_template',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('school_type', sa.String(length=20), nullable=False),
    sa.Column('header_rows', sa.Integer(), nullable=False),
    sa.Column('header_structure', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('column_mapping', sa.Text(), nullable=True),
    sa.Column('validation_rules', sa.Text(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('salary_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('template_id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.String(length=50), nullable=False),
    sa.Column('salary_date', sa.Date(), nullable=False),
    sa.Column('data', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('validation_status', sa.String(length=20), nullable=True),
    sa.Column('validation_message', sa.Text(), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=True),
    sa.Column('process_time', sa.DateTime(), nullable=True),
    sa.Column('source_file', sa.String(length=255), nullable=True),
    sa.Column('source_row', sa.Integer(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['template_id'], ['excel_template.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('salary_data')
    op.drop_table('excel_template')
    # ### end Alembic commands ### 