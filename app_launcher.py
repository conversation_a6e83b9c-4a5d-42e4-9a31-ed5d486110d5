"""
工资管理系统启动器
用于打包成exe文件，双击运行
"""
import os
import sys
import webbrowser
import threading
import time
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量，确保在exe环境下正确运行
os.environ['FLASK_ENV'] = 'production'
os.environ['FLASK_DEBUG'] = '0'

def setup_app_environment():
    """设置应用运行环境"""
    try:
        # 获取exe所在目录或当前脚本目录
        if getattr(sys, 'frozen', False):
            # 运行在打包的exe中
            app_dir = Path(sys.executable).parent
        else:
            # 运行在开发环境中
            app_dir = project_root
        
        print(f"应用目录: {app_dir}")
        
        # 创建必要的目录
        data_dir = app_dir / "data"
        data_dir.mkdir(exist_ok=True)
        
        uploads_dir = app_dir / "uploads"
        uploads_dir.mkdir(exist_ok=True)
        
        (uploads_dir / "high_school").mkdir(exist_ok=True)
        (uploads_dir / "primary_school").mkdir(exist_ok=True)
        
        instance_dir = app_dir / "instance"
        instance_dir.mkdir(exist_ok=True)
        
        print(f"数据目录: {data_dir}")
        print(f"上传目录: {uploads_dir}")
        print(f"实例目录: {instance_dir}")
        
        # 设置数据库路径
        db_path = data_dir / "salary_manager.db"
        
        # 动态设置配置
        import config
        config.Config.SQLALCHEMY_DATABASE_URI = f'sqlite:///{db_path}'
        config.Config.HIGH_SCHOOL_FOLDER = str(uploads_dir / "high_school")
        config.Config.PRIMARY_SCHOOL_FOLDER = str(uploads_dir / "primary_school")
        config.Config.UPLOAD_FOLDER = str(uploads_dir)
        
        print(f"数据库路径: {db_path}")
        
        return True
        
    except Exception as e:
        print(f"环境设置失败: {e}")
        return False

def init_database():
    """初始化数据库"""
    try:
        from app import create_app, db
        
        app = create_app()
        with app.app_context():
            # 创建所有表
            db.create_all()
            print("数据库初始化成功")
            return True
            
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False

def start_flask_app():
    """启动Flask应用"""
    try:
        from app import create_app
        
        app = create_app()
        
        # 禁用Flask的调试输出
        import logging
        log = logging.getLogger('werkzeug')
        log.setLevel(logging.ERROR)
        
        print("正在启动Web服务...")
        
        # 在生产模式下运行
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except Exception as e:
        print(f"启动Flask应用失败: {e}")
        sys.exit(1)

def open_browser():
    """延迟打开浏览器"""
    time.sleep(3)  # 等待Flask应用启动
    
    url = "http://127.0.0.1:5000"
    print(f"正在打开浏览器: {url}")
    
    try:
        webbrowser.open(url)
        print("浏览器已打开")
    except Exception as e:
        print(f"打开浏览器失败: {e}")
        print(f"请手动打开浏览器并访问: {url}")

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    print("\n正在关闭应用...")
    sys.exit(0)

def main():
    """主函数"""
    print("=" * 60)
    print("        教职工工资管理系统 v1.0")
    print("=" * 60)
    print()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # 1. 设置应用环境
        print("1. 设置应用环境...")
        if not setup_app_environment():
            print("环境设置失败，程序退出")
            input("按回车键退出...")
            return
        
        # 2. 初始化数据库
        print("2. 初始化数据库...")
        if not init_database():
            print("数据库初始化失败，程序退出")
            input("按回车键退出...")
            return
        
        print("3. 启动Web服务...")
        print("   服务地址: http://127.0.0.1:5000")
        print("   按 Ctrl+C 可以停止服务")
        print()
        
        # 4. 在后台线程中打开浏览器
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # 5. 启动Flask应用（阻塞运行）
        start_flask_app()
        
    except KeyboardInterrupt:
        print("\n用户中断，正在关闭...")
    except Exception as e:
        print(f"应用运行出错: {e}")
        input("按回车键退出...")
    finally:
        print("应用已关闭")

if __name__ == "__main__":
    main()
