"""调试Excel文件结构"""
import sys
import os
import openpyxl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_excel_file(file_path):
    """调试Excel文件的列结构"""
    print(f"调试Excel文件: {file_path}")
    
    try:
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        
        for sheet_name in workbook.sheetnames:
            print(f"\n=== Sheet: {sheet_name} ===")
            worksheet = workbook[sheet_name]
            
            print(f"原始 max_row: {worksheet.max_row}")
            print(f"原始 max_column: {worksheet.max_column}")
            
            # 智能检测实际列数
            actual_max_col = 0
            consecutive_empty = 0
            
            # 检查前10行
            for col_idx in range(1, min(worksheet.max_column + 1, 1000)):  # 最多检查1000列
                has_data = False
                
                # 检查这一列在前10行是否有数据
                for row_idx in range(1, min(11, worksheet.max_row + 1)):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    if cell.value is not None and str(cell.value).strip():
                        has_data = True
                        break
                
                if has_data:
                    actual_max_col = col_idx
                    consecutive_empty = 0
                    
                    # 显示前50列的内容
                    if col_idx <= 50:
                        values = []
                        for row_idx in range(1, min(6, worksheet.max_row + 1)):
                            cell = worksheet.cell(row=row_idx, column=col_idx)
                            if cell.value is not None:
                                values.append(f"R{row_idx}:'{cell.value}'")
                        if values:
                            print(f"  列{col_idx}: {', '.join(values[:3])}{'...' if len(values) > 3 else ''}")
                else:
                    consecutive_empty += 1
                    # 连续20个空列认为结束
                    if consecutive_empty >= 20:
                        print(f"  连续{consecutive_empty}个空列，停止检查")
                        break
                
                # 进度提示
                if col_idx % 100 == 0:
                    print(f"  已检查到第{col_idx}列...")
            
            print(f"\n实际有数据的最大列: {actual_max_col}")
            print(f"连续空列数: {consecutive_empty}")
            
            # 检查是否有异常的格式信息
            print(f"\n检查列的格式信息:")
            format_count = 0
            for col_idx in range(actual_max_col + 1, min(actual_max_col + 50, worksheet.max_column + 1)):
                cell = worksheet.cell(row=1, column=col_idx)
                if hasattr(cell, 'font') or hasattr(cell, 'fill') or hasattr(cell, 'border'):
                    format_count += 1
            
            print(f"空数据列中有格式的列数: {format_count}")
            
            # 检查合并单元格
            print(f"合并单元格数量: {len(worksheet.merged_cells.ranges)}")
            for i, merged_range in enumerate(worksheet.merged_cells.ranges):
                if i < 10:  # 只显示前10个
                    print(f"  合并单元格{i+1}: {merged_range}")
        
        workbook.close()
        
    except Exception as e:
        print(f"调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        debug_excel_file(sys.argv[1])
    else:
        print("用法: python debug_excel_structure.py <excel_file_path>") 