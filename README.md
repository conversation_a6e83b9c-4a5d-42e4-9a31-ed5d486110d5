# 教职工工资管理系统

这是一个用于管理中小学教职工工资的系统，支持批量导入Excel工资表，并提供查询和导出功能。

## 功能特点

- 支持批量导入中学和小学的工资Excel表
- 自动解析复杂的表头结构（支持多行表头）
- 数据自动合并和存储
- 提供灵活的查询功能
- 支持数据导出

## 安装和运行

1. 创建虚拟环境：
```bash
python -m venv venv
```

2. 激活虚拟环境：
```bash
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 初始化数据库：
```bash
flask db upgrade
```

5. 运行应用：
```bash
flask run
```

## 项目结构

```
.
├── app/                    # 应用主目录
│   ├── static/            # 静态文件
│   ├── templates/         # 模板文件
│   ├── models/           # 数据模型
│   ├── utils/            # 工具函数
│   └── routes/           # 路由处理
├── migrations/            # 数据库迁移文件
├── instance/             # 实例配置
├── tests/                # 测试文件
├── .env                  # 环境变量
├── config.py             # 配置文件
└── run.py               # 应用入口
``` 