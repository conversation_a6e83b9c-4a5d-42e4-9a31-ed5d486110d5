"""
创建 sheet_template 表
"""

from app import create_app, db
from app.models.sheet_template import SheetTemplate

def create_table():
    app = create_app()
    
    with app.app_context():
        print("创建 sheet_template 表...")
        
        try:
            # 创建表
            db.create_all()
            print("✓ sheet_template 表创建成功")
            
            # 验证表是否创建成功
            from sqlalchemy import text
            result = db.session.execute(text("""
                SELECT name FROM sqlite_master WHERE type='table' AND name='sheet_template'
            """)).fetchone()
            
            if result:
                print("✓ 验证：sheet_template 表已存在")
                
                # 查看表结构
                columns = db.session.execute(text("PRAGMA table_info(sheet_template)")).fetchall()
                print("表结构:")
                for col in columns:
                    col_name = col[1]
                    col_type = col[2]
                    not_null = col[3]
                    null_info = "NOT NULL" if not_null else "NULL"
                    print(f"  - {col_name} ({col_type}) {null_info}")
            else:
                print("✗ 表创建失败")
                
        except Exception as e:
            print(f"✗ 创建失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    create_table() 