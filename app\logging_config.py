"""
详细的日志配置模块 - 支持动态日志级别
"""

import logging
import logging.config
import os
import sys
from datetime import datetime

def setup_advanced_logging(app):
    """高级日志配置 - 支持debug模式自动调整级别"""
    
    # 确保日志目录存在
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 根据Flask debug模式和环境变量确定日志级别
    if app.debug or os.getenv('FLASK_DEBUG') == '1' or os.getenv('LOG_LEVEL', '').upper() == 'DEBUG':
        log_level = 'DEBUG'
        console_level = 'DEBUG'
        file_level = 'DEBUG'
        app.logger.info("调试模式：启用DEBUG日志级别")
    else:
        log_level = 'INFO'
        console_level = 'INFO'
        file_level = os.getenv('FILE_LOG_LEVEL', 'INFO').upper()
    
    # 设置系统默认编码（如果需要）
    if sys.stdout.encoding != 'utf-8':
        app.logger.warning(f"系统控制台编码: {sys.stdout.encoding}, 建议使用 UTF-8")
    
    # 详细的日志配置字典
    LOGGING_CONFIG = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'detailed': {
                'format': '%(asctime)s %(levelname)s %(name)s: %(message)s [in %(pathname)s:%(lineno)d]',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'simple': {
                'format': '%(asctime)s %(levelname)s: %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'debug': {
                'format': '%(asctime)s %(levelname)s %(name)s [%(funcName)s:%(lineno)d]: %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': console_level,
                'formatter': 'debug' if log_level == 'DEBUG' else 'detailed',
                'stream': 'ext://sys.stdout'
            },
            'file_daily': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'level': file_level,
                'formatter': 'debug' if log_level == 'DEBUG' else 'detailed',
                'filename': os.path.join(log_dir, 'salary_manager.log'),
                'when': 'midnight',
                'interval': 1,
                'backupCount': 30,
                'encoding': 'utf-8',
                'delay': False
            },
            'error_file': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'level': 'ERROR',
                'formatter': 'detailed',
                'filename': os.path.join(log_dir, 'salary_manager_error.log'),
                'when': 'midnight',
                'interval': 1,
                'backupCount': 30,
                'encoding': 'utf-8',
                'delay': False
            },
            'upload_file': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'level': file_level,
                'formatter': 'debug' if log_level == 'DEBUG' else 'detailed',
                'filename': os.path.join(log_dir, 'salary_manager_upload.log'),
                'when': 'midnight',
                'interval': 1,
                'backupCount': 30,
                'encoding': 'utf-8',
                'delay': False
            }
        },
        'loggers': {
            'app.routes.salary': {
                'level': log_level,
                'handlers': ['console', 'file_daily', 'upload_file'],
                'propagate': False
            },
            'app.utils.excel_parser': {
                'level': log_level,
                'handlers': ['console', 'file_daily'],
                'propagate': False
            }
        },
        'root': {
            'level': log_level,
            'handlers': ['console', 'file_daily', 'error_file']
        }
    }
    
    # 应用配置
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # 更安全的方式设置后缀
    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        if hasattr(handler, 'suffix'):
            handler.suffix = '%Y-%m-%d'
        # 确保编码设置正确
        if hasattr(handler, 'stream') and hasattr(handler.stream, 'encoding'):
            if handler.stream.encoding != 'utf-8':
                app.logger.warning(f"处理器 {handler} 编码: {handler.stream.encoding}")
    
    app.logger.info('高级日志系统已配置')
    app.logger.info(f'当前日志级别: {log_level}')
    app.logger.info(f'日志文件保存在: {os.path.abspath(log_dir)}')
    app.logger.info(f'系统编码信息: stdout={sys.stdout.encoding}, filesystem={sys.getfilesystemencoding()}')
    
    # Debug模式下输出调试信息
    if log_level == 'DEBUG':
        app.logger.debug("这是一条DEBUG级别的测试消息")
        app.logger.debug(f"Flask配置: DEBUG={app.debug}")
        app.logger.debug(f"环境变量: FLASK_DEBUG={os.getenv('FLASK_DEBUG')}, LOG_LEVEL={os.getenv('LOG_LEVEL')}") 