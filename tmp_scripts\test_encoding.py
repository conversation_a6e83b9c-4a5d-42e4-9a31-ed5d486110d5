"""
测试日志文件编码的脚本
"""

from app import create_app
import logging
import os
import sys

def test_encoding():
    print(f"系统信息:")
    print(f"  Python版本: {sys.version}")
    print(f"  默认编码: {sys.getdefaultencoding()}")
    print(f"  文件系统编码: {sys.getfilesystemencoding()}")
    print(f"  控制台编码: {sys.stdout.encoding}")
    print()
    
    app = create_app()
    
    with app.app_context():
        # 测试中文日志
        test_messages = [
            "这是中文测试信息",
            "包含特殊字符: ©®™",
            "员工姓名: 张三",
            "工资项目: 基本工资、绩效奖金、交通补贴",
            "文件名: 2023年5月工资表.xlsx",
            "错误信息: 无法找到"姓名"列"
        ]
        
        app.logger.info("=== 开始编码测试 ===")
        
        for i, message in enumerate(test_messages, 1):
            app.logger.info(f"测试消息 {i}: {message}")
        
        app.logger.warning("这是一条警告消息 - 包含中文字符")
        app.logger.error("这是一条错误消息 - 测试异常情况")
        
        app.logger.info("=== 编码测试完成 ===")
        
        # 检查日志文件
        log_files = []
        log_dir = 'logs'
        if os.path.exists(log_dir):
            for file in os.listdir(log_dir):
                if file.endswith('.log'):
                    log_files.append(os.path.join(log_dir, file))
        
        print(f"生成的日志文件:")
        for log_file in log_files:
            size = os.path.getsize(log_file)
            print(f"  {log_file} ({size} 字节)")
        
        print("\n请用以下方法查看日志文件:")
        print("1. VS Code: 确保右下角编码显示为 'UTF-8'")
        print("2. 记事本: 另存为时选择 'UTF-8' 编码")
        print("3. 命令行: type logs\\salary_manager.log (Windows) 或 cat logs/salary_manager.log (Linux/Mac)")

def read_log_file_test():
    """测试读取日志文件"""
    log_dir = 'logs'
    log_file = os.path.join(log_dir, 'salary_manager.log')
    
    if os.path.exists(log_file):
        print(f"\n=== 直接读取日志文件内容 ===")
        try:
            # 用不同编码尝试读取
            encodings = ['utf-8', 'gbk', 'ascii']
            
            for encoding in encodings:
                try:
                    with open(log_file, 'r', encoding=encoding) as f:
                        content = f.read()
                        print(f"\n使用 {encoding} 编码读取成功:")
                        lines = content.split('\n')
                        for line in lines[-10:]:  # 显示最后10行
                            if line.strip():
                                print(f"  {line}")
                        break
                except UnicodeDecodeError as e:
                    print(f"使用 {encoding} 编码读取失败: {e}")
                    continue
        except Exception as e:
            print(f"读取日志文件失败: {e}")
    else:
        print(f"日志文件不存在: {log_file}")

if __name__ == '__main__':
    test_encoding()
    read_log_file_test() 