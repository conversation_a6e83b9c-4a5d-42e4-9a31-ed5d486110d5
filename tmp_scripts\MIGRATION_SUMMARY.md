# 工资管理系统数据库迁移总结

## 更新内容

### 1. 数据库模型更新 (app/models/salary.py)

为 `SalaryData` 表添加了三个重要字段：

```python
# 新增的重要字段
sheet_name = db.Column(db.String(100), nullable=False, index=True)  # sheet名称
school_type = db.Column(db.String(20), nullable=False, index=True)  # 学校类型（中学/小学）
salary_period = db.Column(db.String(100), nullable=False, index=True)  # 工资周期
```

### 2. 数据导入逻辑更新 (app/routes/salary.py)

#### 主要改进：

1. **处理所有sheets**：
   - 之前只处理第一个sheet
   - 现在处理所有成功解析的sheets

2. **添加新字段支持**：
   ```python
   salary_data = SalaryData(
       template_id=template.id,
       employee_id=row[name_field],  # 使用姓名作为employee_id
       salary_date=datetime.now().date(),  # 记录创建日期
       sheet_name=sheet_name,  # sheet名称
       school_type=school_type,  # 学校类型
       salary_period=salary_period  # 工资周期（文件名）
   )
   ```

3. **智能姓名识别**：
   - 自动查找包含'姓名'、'名字'、'员工姓名'等关键词的列
   - 用姓名代替工号作为员工标识

4. **文件名作为周期**：
   - 直接使用文件名（去除扩展名）作为工资周期
   - 避免JSON序列化问题

### 3. 查询功能增强

#### 新增查询参数：
- `sheet_name`：按sheet名称筛选
- `salary_period`：按工资周期筛选
- 直接使用 `SalaryData.school_type` 而不是关联查询

#### 查询辅助数据：
- `available_sheets`：所有可用的sheet名称
- `available_periods`：所有可用的工资周期

### 4. 数据库迁移脚本

创建了 `migrate_add_fields.py` 脚本：
- 重新创建数据库表结构
- 包含新的字段定义
- 自动添加索引以提高查询性能

## 数据结构变化

### 之前的数据结构问题：
- ❌ 只处理第一个sheet
- ❌ 无法区分学校类型
- ❌ 周期信息不明确
- ❌ 依赖特定的工号和日期字段

### 现在的数据结构优势：
- ✅ 处理所有sheets
- ✅ 明确的学校类型标识
- ✅ 独立的周期字段便于查询
- ✅ 灵活的姓名识别
- ✅ 文件名作为周期，简单明了

## 使用方式

### 1. 运行数据库迁移：
```bash
python migrate_add_fields.py
```

### 2. 上传Excel文件：
- 文件名将作为"所属周期"
- 系统会自动处理所有sheets
- 每条记录都会包含sheet名称和学校类型

### 3. 查询数据：
- 可以按学校类型筛选
- 可以按sheet名称筛选
- 可以按工资周期筛选
- 支持组合查询

## 注意事项

1. **数据迁移会清除现有数据**，因为添加了必需字段
2. **建议在运行迁移前备份重要数据**
3. **新的字段都有索引，查询性能更好**
4. **姓名会作为员工ID，不考虑同名问题**

## 下一步优化建议

1. 可以考虑添加数据导入预览功能
2. 可以添加数据导入历史记录
3. 可以添加数据验证和错误处理机制 