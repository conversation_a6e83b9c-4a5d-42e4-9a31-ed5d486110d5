"""测试完整列数据读取"""
import sys
import os
import openpyxl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.excel_parser_v2 import ExcelParserV2

def test_complete_columns(file_path):
    """测试完整列数据读取"""
    print(f"测试文件: {file_path}")
    
    try:
        # 先用openpyxl直接检查实际数据
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        
        for sheet_name in workbook.sheetnames:
            print(f"\n=== 直接检查 Sheet: {sheet_name} ===")
            worksheet = workbook[sheet_name]
            
            # 检查第一行数据（第5行，假设表头4行）
            data_row = 5
            actual_data = []
            
            for col_idx in range(1, worksheet.max_column + 1):
                cell = worksheet.cell(row=data_row, column=col_idx)
                actual_data.append(cell.value)
            
            print(f"第{data_row}行原始数据 (共{len(actual_data)}列):")
            for i, value in enumerate(actual_data):
                if value is not None:
                    print(f"  列{i+1}: {value}")
                elif i >= len(actual_data) - 10:  # 显示最后10列
                    print(f"  列{i+1}: None")
        
        workbook.close()
        
        # 然后用解析器测试
        print(f"\n=== 解析器测试 ===")
        parser = ExcelParserV2(file_path, header_rows=4)
        result = parser.parse()
        
        for sheet_name, sheet_data in result['sheets'].items():
            print(f"\nSheet: {sheet_name}")
            if 'error' in sheet_data:
                print(f"错误: {sheet_data['error']}")
                continue
            
            print(f"数据行数: {sheet_data['row_count']}")
            
            # 显示列映射
            column_mapping = sheet_data['header_structure']['column_mapping']
            print(f"列映射数量: {len(column_mapping)}")
            print("所有列映射:")
            for col_idx, col_name in column_mapping.items():
                print(f"  Excel列{col_idx+1}: '{col_name}'")
            
            # 显示第一行数据
            if sheet_data['data']:
                first_row = sheet_data['data'][0]
                print(f"\n解析器第一行数据 (共{len(first_row)}列):")
                for i, value in enumerate(first_row):
                    if value or i >= len(first_row) - 10:  # 显示有值的列和最后10列
                        print(f"  索引{i}: {value}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_complete_columns(sys.argv[1])
    else:
        print("用法: python test_complete_columns.py <excel_file_path>") 