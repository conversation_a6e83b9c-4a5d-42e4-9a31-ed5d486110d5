import os
import sys
from pathlib import Path

class Config:
    # 获取应用根目录
    if getattr(sys, 'frozen', False):
        # 运行在打包的exe中
        BASE_DIR = Path(sys.executable).parent
    else:
        # 运行在开发环境中
        BASE_DIR = Path(__file__).parent
    
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # 数据库配置 - 默认值，会在app_launcher.py中动态修改
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f'sqlite:///{BASE_DIR}/data/salary_manager.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 文件上传配置
    UPLOAD_FOLDER = str(BASE_DIR / 'uploads')
    HIGH_SCHOOL_FOLDER = str(BASE_DIR / 'uploads' / 'high_school')
    PRIMARY_SCHOOL_FOLDER = str(BASE_DIR / 'uploads' / 'primary_school')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {'xlsx', 'xls'}
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = str(BASE_DIR / 'logs' / 'app.log')
    
    @classmethod
    def init_app(cls, app):
        """初始化应用配置"""
        # 确保必要的目录存在
        directories = [
            cls.BASE_DIR / 'data',
            cls.BASE_DIR / 'uploads',
            cls.BASE_DIR / 'uploads' / 'high_school',
            cls.BASE_DIR / 'uploads' / 'primary_school',
            cls.BASE_DIR / 'instance',
            cls.BASE_DIR / 'logs'
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}