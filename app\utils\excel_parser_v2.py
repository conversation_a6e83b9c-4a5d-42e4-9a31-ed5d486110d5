import openpyxl
from openpyxl.utils import get_column_letter, column_index_from_string
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class ExcelParserV2:
    """使用openpyxl的新版Excel解析器 - 完美支持合并单元格"""
    
    def __init__(self, file_path, header_rows=4):
        self.file_path = file_path
        self.header_rows = header_rows
        self.workbook = None
        
    def parse(self):
        """解析Excel文件"""
        try:
            logger.info(f"开始解析Excel文件: {self.file_path}")
            self.workbook = openpyxl.load_workbook(self.file_path, data_only=True)
            
            result = {
                'sheets': {},
                'total_records': 0,
                'parsed_at': datetime.now().isoformat()
            }
            
            for sheet_name in self.workbook.sheetnames:
                logger.info(f"解析Sheet: {sheet_name}")
                worksheet = self.workbook[sheet_name]
                
                try:
                    sheet_result = self.parse_sheet(worksheet, sheet_name)
                    result['sheets'][sheet_name] = sheet_result
                    result['total_records'] += len(sheet_result.get('data', []))
                except Exception as e:
                    logger.error(f"解析Sheet '{sheet_name}' 失败: {str(e)}")
                    result['sheets'][sheet_name] = {'error': str(e)}
            
            return result
            
        except Exception as e:
            logger.error(f"解析Excel文件失败: {str(e)}")
            raise
        finally:
            if self.workbook:
                self.workbook.close()
    
    def parse_sheet(self, worksheet, sheet_name):
        """解析单个工作表"""
        # 1. 获取表头结构
        header_structure = self.analyze_header_structure(worksheet)
        
        # 2. 获取数据行
        data_rows = self.extract_data_rows(worksheet, header_structure)
        
        # 3. 添加姓名列索引信息
        name_col_index = self.find_name_column_index(header_structure['column_mapping'])
        
        return {
            'header_structure': header_structure,
            'data': data_rows,
            'row_count': len(data_rows),
            'name_column_index': name_col_index  # 新增：姓名列索引
        }
    
    def analyze_header_structure(self, worksheet):
        """分析表头结构 - 使用openpyxl的合并单元格信息"""
        logger.info(f"分析表头结构，表头行数: {self.header_rows}")
        
        # 智能检测实际有数据的列范围
        max_col = self.get_actual_max_column(worksheet)
        logger.info(f"检测到实际最大列数: {max_col} (原max_column: {worksheet.max_column})")
        
        # 如果列数仍然很大，给出警告
        if max_col > 500:
            logger.warning(f"检测到异常大的列数({max_col})，可能存在数据质量问题")
        
        # 获取所有合并单元格信息
        merged_cells_info = []
        for merged_range in worksheet.merged_cells.ranges:
            min_row, min_col = merged_range.min_row, merged_range.min_col
            max_row, max_col_range = merged_range.max_row, merged_range.max_col
            
            # 只处理表头范围内的合并单元格，且在实际列范围内
            if min_row <= self.header_rows and min_col <= max_col:
                merged_info = {
                    'row': min_row - 1,  # 转换为0基索引
                    'col': min_col - 1,  # 转换为0基索引
                    'rowspan': max_row - min_row + 1,
                    'colspan': max_col_range - min_col + 1,
                    'range': str(merged_range)
                }
                merged_cells_info.append(merged_info)
                logger.debug(f"发现合并单元格: {merged_info}")
        
        # 获取表头的实际内容
        header_levels = []
        
        for row_idx in range(self.header_rows):
            level_cells = []
            for col_idx in range(max_col):
                cell = worksheet.cell(row=row_idx + 1, column=col_idx + 1)
                
                # 检查这个单元格是否是合并单元格的起始位置
                is_merged_start = False
                merged_info = None
                for merged in merged_cells_info:
                    if merged['row'] == row_idx and merged['col'] == col_idx:
                        is_merged_start = True
                        merged_info = merged
                        break
                
                # 检查这个单元格是否被其他合并单元格覆盖
                is_covered = False
                covering_merge = None
                for merged in merged_cells_info:
                    if (merged['row'] <= row_idx < merged['row'] + merged['rowspan'] and
                        merged['col'] <= col_idx < merged['col'] + merged['colspan'] and
                        not (merged['row'] == row_idx and merged['col'] == col_idx)):
                        is_covered = True
                        covering_merge = merged
                        break
                
                cell_info = {
                    'row': row_idx,
                    'col': col_idx,
                    'value': str(cell.value) if cell.value is not None else '',
                    'is_merged_start': is_merged_start,
                    'is_covered': is_covered,
                    'rowspan': merged_info['rowspan'] if merged_info else 1,
                    'colspan': merged_info['colspan'] if merged_info else 1
                }
                
                if is_covered:
                    cell_info['covered_by'] = covering_merge['range']
                
                level_cells.append(cell_info)
                
                # 只在DEBUG模式下记录详细信息，且限制输出量
                if logger.isEnabledFor(logging.DEBUG) and col_idx < 30:  # 只记录前30列
                    logger.debug(f"单元格 ({row_idx},{col_idx}): 值='{cell_info['value']}', "
                               f"合并起始={is_merged_start}, 被覆盖={is_covered}, "
                               f"rowspan={cell_info['rowspan']}, colspan={cell_info['colspan']}")
                elif col_idx == 30 and logger.isEnabledFor(logging.DEBUG):
                    logger.debug(f"... (省略剩余{max_col-30}列的详细日志)")
            
            header_levels.append(level_cells)
        
        # 生成列映射（找到数据列的对应关系）
        column_mapping = self.generate_column_mapping(header_levels)
        
        logger.info(f"表头分析完成，实际列数: {max_col}, 有效列映射: {len(column_mapping)}")
        
        # 如果有效列映射太少，给出警告
        if len(column_mapping) < max_col * 0.3:
            logger.warning(f"有效列映射({len(column_mapping)})相对实际列数({max_col})较少，可能存在空列")
        
        return {
            'levels': header_levels,
            'merged_cells': merged_cells_info,
            'column_mapping': column_mapping,
            'total_rows': self.header_rows,
            'total_cols': max_col
        }
    
    def generate_column_mapping(self, header_levels):
        """生成列映射关系 - 确保所有列都被包含，包括空表头列"""
        column_mapping = {}
        
        # 获取每个数据列的完整路径名称
        if not header_levels:
            return column_mapping
        
        max_cols = len(header_levels[0])
        
        for col_idx in range(max_cols):
            # 构建这一列的完整路径
            column_path = []
            
            for row_idx in range(len(header_levels)):
                cell = header_levels[row_idx][col_idx]
                
                # 跳过被覆盖的单元格
                if cell['is_covered']:
                    continue
                
                # 如果有值，添加到路径中
                if cell['value'].strip():
                    column_path.append(cell['value'].strip())
            
            # 生成列名（确保每一列都有名称）
            if column_path:
                # 如果有表头内容
                if len(column_path) == 1:
                    column_name = column_path[0]
                else:
                    # 多层级：使用最后一级作为主名称，前面的作为前缀（如果需要区分的话）
                    column_name = column_path[-1]
                    
                    # 检查是否有重名，如果有则添加前缀
                    existing_names = list(column_mapping.values())
                    if column_name in existing_names:
                        # 添加父级作为前缀
                        if len(column_path) > 1:
                            column_name = f"{column_path[-2]}_{column_name}"
                        else:
                            column_name = f"列{col_idx}_{column_name}"
            else:
                # 如果没有表头内容，使用默认列名
                column_name = f"列{col_idx + 1}"  # 从1开始编号，更符合Excel习惯
            
            # 确保列名唯一
            original_name = column_name
            counter = 1
            while column_name in column_mapping.values():
                column_name = f"{original_name}_{counter}"
                counter += 1
            
            column_mapping[col_idx] = column_name
            logger.debug(f"列{col_idx}: {' -> '.join(column_path) if column_path else '(空表头)'} => '{column_name}'")
        
        return column_mapping
    
    def extract_data_rows(self, worksheet, header_structure):
        """提取数据行 - 读取所有列的数据"""
        data_rows = []
        column_mapping = header_structure['column_mapping']
        total_keywords = ['合计', '总计', '小计', '汇总', '总和', '累计']
        
        # 从表头后开始读取数据
        start_row = self.header_rows + 1
        max_row = worksheet.max_row
        
        # 获取实际的最大列数（应该与column_mapping的长度一致）
        actual_max_col = max(column_mapping.keys()) + 1 if column_mapping else 0
        
        logger.info(f"开始提取数据行，从第{start_row}行到第{max_row}行，数据列数: {actual_max_col}")
        
        # 精准识别姓名列
        name_col_index = self.find_name_column_index(column_mapping)
        logger.info(f"识别到姓名列索引: {name_col_index}")
        
        for row_idx in range(start_row, max_row + 1):
            row_data = []  # 使用数组而不是字典
            is_empty_row = True
            is_total_row = False
            
            # 按列顺序读取数据 - 确保读取所有列
            for col_idx in range(actual_max_col):
                cell = worksheet.cell(row=row_idx, column=col_idx + 1)
                cell_value = cell.value
                
                # 格式化数值
                if isinstance(cell_value, (int, float)):
                    if float(cell_value).is_integer():
                        formatted_value = int(cell_value)
                    else:
                        formatted_value = round(float(cell_value), 2)
                elif isinstance(cell_value, str):
                    formatted_value = cell_value.strip()
                elif cell_value is None:
                    formatted_value = ''
                else:
                    formatted_value = str(cell_value)
                
                row_data.append(formatted_value)
                
                # 检查是否为空行
                if formatted_value and str(formatted_value).strip():
                    is_empty_row = False
                
                # 检查是否为合计行
                if formatted_value and any(keyword in str(formatted_value) for keyword in total_keywords):
                    is_total_row = True
                    logger.info(f"发现合计行(第{row_idx}行): 列{col_idx}='{formatted_value}'")
            
            # 跳过空行
            if is_empty_row:
                logger.debug(f"跳过空行: 第{row_idx}行")
                continue
            
            # 如果遇到合计行，停止处理
            if is_total_row:
                logger.info(f"遇到合计行，停止处理后续数据")
                break
            
            # 检查姓名列是否有效
            if name_col_index is not None and name_col_index < len(row_data):
                name_value = str(row_data[name_col_index]).strip()
                if not name_value or any(keyword in name_value for keyword in total_keywords):
                    logger.debug(f"跳过无效姓名的行: '{name_value}'")
                    continue
            
            data_rows.append(row_data)
            logger.debug(f"添加数据行: 第{row_idx}行，数据长度={len(row_data)}")
        
        logger.info(f"数据提取完成，共{len(data_rows)}行有效数据")
        return data_rows
    
    def find_name_column_index(self, column_mapping):
        """精准识别姓名列的索引位置"""
        name_keywords = ['姓名', '名字', '员工姓名', '人员姓名', '员工', '人员']
        
        # 首先尝试精确匹配
        for excel_col_idx, col_name in column_mapping.items():
            if col_name in ['姓名', '员工姓名', '人员姓名']:
                array_index = list(column_mapping.keys()).index(excel_col_idx)
                logger.info(f"精确匹配到姓名列: '{col_name}' (Excel列{excel_col_idx}, 数组索引{array_index})")
                return array_index
        
        # 然后尝试关键词匹配
        for excel_col_idx, col_name in column_mapping.items():
            for keyword in name_keywords:
                if keyword in col_name:
                    array_index = list(column_mapping.keys()).index(excel_col_idx)
                    logger.info(f"关键词匹配到姓名列: '{col_name}' 包含'{keyword}' (Excel列{excel_col_idx}, 数组索引{array_index})")
                    return array_index
        
        logger.warning("未找到姓名列，将使用第一列作为姓名列")
        return 0 if column_mapping else None
    
    def get_actual_max_column(self, worksheet):
        """智能检测实际有数据的最大列数"""
        logger.info(f"开始检测实际最大列数，Excel报告的max_column: {worksheet.max_column}")
        
        # 如果max_column本身就很合理（比如小于200），直接使用
        if worksheet.max_column <= 200:
            logger.info(f"Excel列数合理({worksheet.max_column})，直接使用")
            return worksheet.max_column
        
        # 对于异常大的列数，需要智能检测
        actual_max_col = 1
        consecutive_empty_cols = 0
        MAX_CONSECUTIVE_EMPTY = 20  # 连续20个空列就认为数据结束了
        
        # 检查表头区域和前几行数据
        check_rows = list(range(1, min(self.header_rows + 6, worksheet.max_row + 1)))
        
        for col_idx in range(1, worksheet.max_column + 1):
            has_data_in_col = False
            
            # 检查这一列在指定行范围内是否有数据
            for row_idx in check_rows:
                cell = worksheet.cell(row=row_idx, column=col_idx)
                if cell.value is not None and str(cell.value).strip():
                    has_data_in_col = True
                    break
            
            if has_data_in_col:
                actual_max_col = col_idx
                consecutive_empty_cols = 0
                logger.debug(f"列{col_idx}有数据，更新actual_max_col为{actual_max_col}")
            else:
                consecutive_empty_cols += 1
                
                # 如果连续遇到太多空列，认为数据已经结束
                if consecutive_empty_cols >= MAX_CONSECUTIVE_EMPTY:
                    logger.info(f"连续{consecutive_empty_cols}个空列，认为数据结束于列{actual_max_col}")
                    break
            
            # 添加进度日志，避免过长时间无响应
            if col_idx % 100 == 0:
                logger.debug(f"已检查到第{col_idx}列，当前actual_max_col: {actual_max_col}")
        
        # 确保至少有一列
        actual_max_col = max(1, actual_max_col)
        
        logger.info(f"智能检测完成 - 原始最大列: {worksheet.max_column}, 实际有数据的最大列: {actual_max_col}")
        return actual_max_col 