{% extends "base.html" %}

{% block title %}首页{% endblock %}

{% block content %}
<div class="text-center">
    <h1 class="text-4xl font-bold text-gray-800 mb-8">欢迎使用教职工工资管理系统</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <h2 class="text-2xl font-semibold text-primary mb-4">导入工资表</h2>
            <p class="text-gray-600 mb-4">批量导入中学和小学教职工的工资表，支持多个工资表同时导入。</p>
            <a href="{{ url_for('salary.upload') }}" class="inline-block bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-800 transition-colors">
                开始导入
            </a>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <h2 class="text-2xl font-semibold text-primary mb-4">查询工资</h2>
            <p class="text-gray-600 mb-4">快速查询教职工的工资信息，支持按姓名、工号、时间等多种方式查询。</p>
            <a href="{{ url_for('salary.search') }}" class="inline-block bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-800 transition-colors">
                开始查询
            </a>
        </div>
    </div>
    
    <div class="mt-12 bg-white rounded-lg shadow-md p-6 max-w-4xl mx-auto">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">系统特点</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-left">
                <h3 class="text-lg font-semibold text-primary mb-2">批量导入</h3>
                <p class="text-gray-600">支持同时导入多个Excel工资表，自动识别表格结构。</p>
            </div>
            <div class="text-left">
                <h3 class="text-lg font-semibold text-primary mb-2">智能解析</h3>
                <p class="text-gray-600">自动处理复杂的表头结构，支持多行表头的解析。</p>
            </div>
            <div class="text-left">
                <h3 class="text-lg font-semibold text-primary mb-2">灵活导出</h3>
                <p class="text-gray-600">支持查询结果导出为Excel，方便进行后续处理。</p>
            </div>
        </div>
    </div>
</div>
{% endblock %} 