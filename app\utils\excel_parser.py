import pandas as pd
import numpy as np
from datetime import datetime
import os
import logging
import traceback
from openpyxl.utils import get_column_letter

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ExcelParser:
    def __init__(self, file_path, header_rows):
        self.file_path = file_path
        self.header_rows = header_rows
        self.df = None
        self.header_structure = None
        logger.info(f"初始化ExcelParser: 文件={file_path}, 表头行数={header_rows}")

    def _column_index_to_excel_name(self, index):
        """将列索引转换为Excel列名 (0->A, 1->B, ..., 25->Z, 26->AA, ...)"""
        result = ""
        while index >= 0:
            result = chr(index % 26 + ord('A')) + result
            index = index // 26 - 1
            if index < 0:
                break
        return result

    def analyze_header_structure(self):
        """分析表头结构 - 保留所有单元格包括空单元格"""
        logger.debug("开始分析表头结构...")
        header_df = self.df.iloc[:self.header_rows]
        
        # 初始化表头结构
        self.header_structure = {
            'levels': [],  # 每一层的表头
            'merged_cells': [],  # 合并单元格信息
            'final_columns': []  # 最终的列名
        }
        
        # 保存每一层表头数据（保留空单元格）
        for i in range(self.header_rows):
            row = header_df.iloc[i]
            level = []
            for j, cell in enumerate(row):
                # 保留空单元格，但转换为空字符串
                if pd.isna(cell):
                    cell_value = ''
                else:
                    cell_value = str(cell).strip()
                level.append({'value': cell_value, 'col_index': j})
            self.header_structure['levels'].append(level)
            # 修复语法错误：将复杂表达式提取出来
            level_debug = [f"{cell['col_index']}:\"{cell['value']}\"" for cell in level]
            logger.debug(f"第{i+1}层表头: {level_debug}")
        
        # 按照新逻辑生成最终列名：从最后一行开始向上查找，保留空单元格位置
        num_columns = len(header_df.columns)
        final_columns = []
        
        for col_idx in range(num_columns):
            column_name = None
            
            # 从最后一行开始向上查找第一个非空值
            for row_idx in range(self.header_rows - 1, -1, -1):
                cell_value = header_df.iloc[row_idx, col_idx]
                
                # 检查是否为有效值（非空且不是NaN）
                if not pd.isna(cell_value) and str(cell_value).strip() != '':
                    column_name = str(cell_value).strip()
                    logger.debug(f"列{col_idx}: 在第{row_idx+1}行找到表头 '{column_name}'")
                    break
            
            # 如果没有找到有效值，使用Excel列名格式但保留该列
            if column_name is None or column_name == '':
                excel_col_name = self._column_index_to_excel_name(col_idx)
                column_name = f'col_{excel_col_name}'
                logger.debug(f"列{col_idx}: 空列，使用Excel列名格式 '{column_name}'")
            
            final_columns.append(column_name)
        
        self.header_structure['final_columns'] = final_columns
        logger.debug(f"最终列名（包含空列）: {final_columns}")
        
        # 分析合并单元格信息
        self._analyze_all_merged_cells()
        
        logger.info("表头结构分析完成")

    def _analyze_all_merged_cells(self):
        """综合分析水平和垂直合并单元格 - 修复covered检查逻辑"""
        try:
            logger.debug("开始分析所有合并单元格...")
            
            levels = self.header_structure['levels']
            merged_cells = []
            
            # 首先打印每一层的原始数据
            logger.info("=" * 60)
            logger.info("表头层级详细分析:")
            for level_idx, level in enumerate(levels):
                level_values = [cell['value'] for cell in level]
                logger.info(f"第{level_idx + 1}层 (索引{level_idx}): {level_values}")
            logger.info("=" * 60)
            
            # 创建一个二维数组来跟踪哪些单元格已经被合并覆盖
            max_cols = max(len(level) for level in levels) if levels else 0
            covered = [[False for _ in range(max_cols)] for _ in range(len(levels))]
            
            # 遍历每个单元格
            for row_idx in range(len(levels)):
                for col_idx in range(len(levels[row_idx])):
                    # 检查当前单元格是否已被之前的合并覆盖
                    if covered[row_idx][col_idx]:
                        logger.debug(f"跳过已被覆盖的单元格 [{row_idx},{col_idx}]")
                        continue
                    
                    cell_value = levels[row_idx][col_idx]['value']
                    logger.debug(f"分析单元格 [{row_idx},{col_idx}]: '{cell_value}'")
                    
                    # 跳过空单元格（空单元格不能作为合并的起始点）
                    if not cell_value:
                        logger.debug(f"  跳过空单元格")
                        continue
                    
                    # 计算水平合并范围
                    colspan = 1
                    for c in range(col_idx + 1, len(levels[row_idx])):
                        # 检查目标单元格是否已被覆盖
                        if covered[row_idx][c]:
                            logger.debug(f"  水平合并停止: [{row_idx},{c}] 已被覆盖")
                            break
                        
                        next_cell = levels[row_idx][c]['value']
                        logger.debug(f"  检查水平合并 [{row_idx},{c}]: '{next_cell}'")
                        
                        # 合并条件：目标单元格为空 或 值相同
                        if not next_cell or next_cell == cell_value:
                            colspan += 1
                            logger.debug(f"    可合并: colspan = {colspan}")
                        else:
                            logger.debug(f"    停止合并: 不同值 '{next_cell}'")
                            break
                    
                    # 计算垂直合并范围
                    rowspan = 1
                    for r in range(row_idx + 1, len(levels)):
                        # 检查列索引是否超出范围
                        if col_idx >= len(levels[r]):
                            logger.debug(f"  垂直合并停止: [{r},{col_idx}] 超出列范围")
                            break
                        
                        # 检查目标单元格是否已被覆盖
                        if covered[r][col_idx]:
                            logger.debug(f"  垂直合并停止: [{r},{col_idx}] 已被覆盖")
                            break
                        
                        next_cell = levels[r][col_idx]['value']
                        logger.debug(f"  检查垂直合并 [{r},{col_idx}]: '{next_cell}'")
                        
                        # 合并条件：目标单元格为空 或 值相同
                        if not next_cell or next_cell == cell_value:
                            rowspan += 1
                            logger.debug(f"    可合并: rowspan = {rowspan}")
                        else:
                            logger.debug(f"    停止合并: 不同值 '{next_cell}'")
                            break
                    
                    # 如果发现合并单元格，记录并标记覆盖区域
                    if colspan > 1 or rowspan > 1:
                        merged_info = {
                            'row': row_idx,
                            'col': col_idx,
                            'rowspan': rowspan,
                            'colspan': colspan,
                            'value': cell_value
                        }
                        merged_cells.append(merged_info)
                        logger.info(f"★ 发现合并单元格: 位置[{row_idx},{col_idx}] 值='{cell_value}' rowspan={rowspan} colspan={colspan}")
                        
                        # 标记所有被此合并单元格覆盖的区域
                        cover_info = []
                        for r in range(row_idx, row_idx + rowspan):
                            for c in range(col_idx, col_idx + colspan):
                                if r < len(covered) and c < len(covered[r]):
                                    covered[r][c] = True
                                    cover_info.append(f"[{r},{c}]")
                        logger.info(f"  覆盖区域: {' '.join(cover_info)}")
                        
                        # 验证覆盖区域的值
                        logger.debug(f"  验证覆盖区域的值:")
                        for r in range(row_idx, row_idx + rowspan):
                            for c in range(col_idx, col_idx + colspan):
                                if r < len(levels) and c < len(levels[r]):
                                    verify_value = levels[r][c]['value']
                                    logger.debug(f"    [{r},{c}]: '{verify_value}'")
                    else:
                        logger.debug(f"  普通单元格: [{row_idx},{col_idx}] = '{cell_value}'")
            
            self.header_structure['merged_cells'] = merged_cells
            
            # 输出最终的合并单元格汇总
            logger.info("=" * 60)
            logger.info("合并单元格汇总:")
            if merged_cells:
                for i, mc in enumerate(merged_cells, 1):
                    logger.info(f"{i}. 位置[{mc['row']},{mc['col']}] '{mc['value']}' -> rowspan={mc['rowspan']}, colspan={mc['colspan']}")
            else:
                logger.info("未发现任何合并单元格")
            logger.info("=" * 60)
            
            logger.debug(f"合并单元格分析完成，共找到 {len(merged_cells)} 个合并单元格")
            
        except Exception as e:
            logger.error(f"分析合并单元格时发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            # 不抛出异常，因为这不影响主要功能

    def parse(self):
        """解析Excel文件 - 支持多个sheet，保持原始数据结构"""
        try:
            logger.info(f"开始解析Excel文件: {self.file_path}")
            
            # 读取Excel文件获取所有sheet信息
            logger.debug("获取Excel文件的所有sheet...")
            xlsx_file = pd.ExcelFile(self.file_path)
            sheet_names = xlsx_file.sheet_names
            logger.info(f"发现 {len(sheet_names)} 个sheet: {sheet_names}")
            
            # 存储所有sheet的解析结果
            result = {'sheets': {}}
            
            # 遍历每个sheet进行解析
            for sheet_name in sheet_names:
                logger.info(f"开始解析sheet: {sheet_name}")
                
                try:
                    # 读取当前sheet，不自动处理表头
                    logger.debug(f"读取sheet '{sheet_name}'...")
                    self.df = pd.read_excel(
                        xlsx_file,
                        sheet_name=sheet_name,
                        header=None,
                        na_values=[''], 
                        keep_default_na=False
                    )
                    logger.debug(f"Sheet '{sheet_name}' 读取成功，形状: {self.df.shape}")
                    
                    # 检查sheet是否为空
                    if self.df.empty:
                        logger.warning(f"Sheet '{sheet_name}' 为空，跳过处理")
                        continue
                    
                    # 检查表头行数是否超过实际行数
                    if self.header_rows > len(self.df):
                        logger.warning(f"Sheet '{sheet_name}' 的行数({len(self.df)})少于指定的表头行数({self.header_rows})，跳过处理")
                        continue
                    
                    # 分析表头结构
                    logger.debug(f"分析sheet '{sheet_name}' 的表头结构...")
                    self.analyze_header_structure()
                    logger.debug(f"Sheet '{sheet_name}' 表头结构分析完成: {len(self.header_structure['levels'])} 层")
                    
                    # 处理数据部分 - 保持原始结构
                    logger.debug(f"处理sheet '{sheet_name}' 的数据部分，跳过前 {self.header_rows} 行...")
                    data_df = self.df.iloc[self.header_rows:].copy()
                    
                    # 检查数据部分是否为空
                    if data_df.empty:
                        logger.warning(f"Sheet '{sheet_name}' 的数据部分为空")
                        result['sheets'][sheet_name] = {
                            'header_structure': self.header_structure.copy(),
                            'data': [],
                            'row_count': 0
                        }
                        continue
                    
                    # 使用analyze_header_structure解析出的列名
                    data_df.columns = self.header_structure['final_columns']
                    
                    # 只移除完全空白的行，保持其他原始数据
                    data_df = data_df.dropna(how='all')
                    
                    # 将NaN值转换为空字符串，保持前端显示一致性
                    data_df = data_df.fillna('')
                    
                    logger.debug(f"Sheet '{sheet_name}' 数据处理完成，最终形状: {data_df.shape}")
                    
                    # 存储当前sheet的结果
                    result['sheets'][sheet_name] = {
                        'header_structure': self.header_structure.copy(),  # 复制避免被后续sheet覆盖
                        'data': data_df.to_dict('records'),
                        'row_count': len(data_df)
                    }
                    
                    logger.info(f"Sheet '{sheet_name}' 解析完成，数据行数: {len(data_df)}")
                    
                except Exception as sheet_error:
                    logger.error(f"解析sheet '{sheet_name}' 时发生错误: {str(sheet_error)}")
                    logger.error("错误堆栈跟踪:")
                    logger.error(traceback.format_exc())
                    
                    # 记录错误但继续处理其他sheet
                    result['sheets'][sheet_name] = {
                        'error': str(sheet_error),
                        'header_structure': None,
                        'data': [],
                        'row_count': 0
                    }
            
            # 关闭Excel文件
            xlsx_file.close()
            
            # 添加汇总信息
            successful_sheets = [name for name, data in result['sheets'].items() if 'error' not in data]
            failed_sheets = [name for name, data in result['sheets'].items() if 'error' in data]
            total_rows = sum(data.get('row_count', 0) for data in result['sheets'].values())
            
            result['summary'] = {
                'total_sheets': len(sheet_names),
                'successful_sheets': len(successful_sheets),
                'failed_sheets': len(failed_sheets),
                'successful_sheet_names': successful_sheets,
                'failed_sheet_names': failed_sheets,
                'total_data_rows': total_rows
            }
            
            logger.info(f"Excel文件解析完成 - 成功: {len(successful_sheets)}个sheet, 失败: {len(failed_sheets)}个sheet, 总数据行数: {total_rows}")
            return result
            
        except Exception as e:
            logger.error(f"解析Excel文件时发生错误: {str(e)}")
            logger.error("错误堆栈跟踪:")
            logger.error(traceback.format_exc())
            raise

    def _analyze_vertical_merges(self):
        """分析垂直方向的合并单元格"""
        try:
            logger.debug("开始分析垂直合并单元格...")
            header_df = self.df.iloc[:self.header_rows]
            
            # 遍历每一列
            for col_idx in range(len(header_df.columns)):
                logger.debug(f"分析第 {col_idx + 1} 列的垂直合并...")
                current_value = None
                start_row = 0
                span_count = 0
                
                # 遍历每一行
                for row_idx in range(self.header_rows):
                    value = header_df.iloc[row_idx, col_idx]
                    
                    # 如果值相同或为空，可能是垂直合并
                    if pd.isna(value) or (current_value and value == current_value):
                        span_count += 1
                    else:
                        # 如果有前一个值且跨度大于1，添加合并信息
                        if current_value and span_count > 0:
                            merged_info = {
                                'row': start_row,
                                'col': col_idx,
                                'rowspan': span_count + 1,
                                'colspan': 1,
                                'value': current_value
                            }
                            logger.debug(f"添加垂直合并单元格信息: {merged_info}")
                            self.header_structure['merged_cells'].append(merged_info)
                        
                        # 开始新的值
                        current_value = value
                        start_row = row_idx
                        span_count = 0
                
                # 处理最后一个值
                if current_value and span_count > 0:
                    merged_info = {
                        'row': start_row,
                        'col': col_idx,
                        'rowspan': span_count + 1,
                        'colspan': 1,
                        'value': current_value
                    }
                    logger.debug(f"添加最后的垂直合并单元格信息: {merged_info}")
                    self.header_structure['merged_cells'].append(merged_info)
            
            logger.info("垂直合并单元格分析完成")
            
        except Exception as e:
            logger.error(f"分析垂直合并单元格时发生错误: {str(e)}")
            logger.error("错误堆栈跟踪:")
            logger.error(traceback.format_exc())
            raise
    
    def parse_salary_date(self):
        """从文件名解析工资日期"""
        # 假设文件名格式包含年月信息，如：2023年09月工资表.xlsx
        filename = os.path.splitext(self.file_path)[0]
        try:
            # 提取年月信息
            year = int(''.join(filter(str.isdigit, filename.split('年')[0])))
            month = int(''.join(filter(str.isdigit, filename.split('年')[1].split('月')[0])))
            return datetime(year, month, 1).date()
        except:
            # 如果无法从文件名解析，返回当前日期
            return datetime.now().date().replace(day=1)
    
    def parse_header(self, sheet):
        """解析复杂的表头（处理多行表头）"""
        # 读取前4行作为表头
        headers = []
        for i in range(4):
            row = sheet.iloc[i]
            headers.append([str(cell) for cell in row])
            
        # 合并多行表头
        merged_headers = []
        for col_idx in range(len(headers[0])):
            header_parts = []
            for row_idx in range(4):
                cell = headers[row_idx][col_idx]
                if cell and cell != 'nan':
                    header_parts.append(cell)
            merged_headers.append('_'.join(header_parts))
            
        return merged_headers
    
    def parse_excel(self):
        """解析Excel文件"""
        # 读取所有sheet
        xlsx = pd.ExcelFile(self.file_path)
        all_data = []
        
        for sheet_name in xlsx.sheet_names:
            # 读取sheet，跳过前4行表头
            df = pd.read_excel(xlsx, sheet_name=sheet_name, header=None)
            
            # 解析表头
            headers = self.parse_header(df)
            
            # 使用解析后的表头重新读取数据
            df = pd.read_excel(xlsx, sheet_name=sheet_name, skiprows=4)
            df.columns = headers
            
            # 添加学校类型
            df['school_type'] = self.school_type
            
            # 添加工资月份
            df['salary_date'] = self.parse_salary_date()
            
            # 添加源文件信息
            df['source_file'] = self.file_path
            
            all_data.append(df)
            
        # 合并所有sheet的数据
        if all_data:
            return pd.concat(all_data, ignore_index=True)
        return pd.DataFrame()

    def parse_sheet(self, sheet, sheet_name):
        """解析单个sheet"""
        try:
            # ... existing code ...
            
            # 构建完整的列名映射（包含路径信息）
            full_column_names = self._build_full_column_names(header_structure)
            
            # 解析数据行
            data_rows = []
            for row_idx in range(self.header_rows, sheet.max_row + 1):
                if self._is_empty_row(sheet, row_idx):
                    continue
                    
                row_data = {}
                for col_idx, (col_letter, full_col_name) in enumerate(full_column_names.items(), 1):
                    cell_value = sheet.cell(row=row_idx, column=col_idx).value
                    if cell_value is not None:
                        # 处理数值格式
                        if isinstance(cell_value, (int, float)):
                            # 保留两位小数，四舍五入
                            cell_value = round(float(cell_value), 2)
                        row_data[full_col_name] = cell_value
                
                if row_data:  # 只添加非空行
                    data_rows.append(row_data)
            
            # ... existing code ...
            
        except Exception as e:
            return {'error': str(e)}

    def _build_full_column_names(self, header_structure):
        """构建包含完整路径的列名映射"""
        full_names = {}
        if not header_structure or not header_structure['levels']:
            return full_names
        
        levels = header_structure['levels']
        merged_cells = header_structure.get('merged_cells', [])
        
        # 最后一层是实际的数据列
        last_level = levels[-1]
        
        for col_idx, cell in enumerate(last_level):
            col_letter = get_column_letter(col_idx + 1)
            base_name = cell.get('value', f'Column_{col_idx + 1}')
            
            # 构建完整路径
            path_parts = []
            
            # 从上往下遍历每一层，构建路径
            for level_idx in range(len(levels) - 1):  # 排除最后一层
                level = levels[level_idx]
                
                # 找到当前列在这一层对应的合并单元格
                parent_cell = self._find_parent_cell(level_idx, col_idx, merged_cells, levels)
                if parent_cell and parent_cell.strip():
                    path_parts.append(parent_cell.strip())
            
            # 构建完整的列名
            if path_parts:
                full_name = '_'.join(path_parts + [base_name])
            else:
                full_name = base_name
                
            # 处理重复名称
            original_full_name = full_name
            counter = 1
            while full_name in full_names.values():
                full_name = f"{original_full_name}_{counter}"
                counter += 1
                
            full_names[col_letter] = full_name
        
        return full_names

    def _find_parent_cell(self, level_idx, col_idx, merged_cells, levels):
        """找到指定列在指定层级的父单元格内容"""
        level = levels[level_idx]
        
        # 查找覆盖当前列的合并单元格
        for merged in merged_cells:
            if (merged['row'] == level_idx and 
                merged['col'] <= col_idx < merged['col'] + merged['colspan']):
                # 找到对应的单元格内容
                if merged['col'] < len(level):
                    return level[merged['col']].get('value', '')
        
        # 如果没有合并单元格，直接取对应位置的值
        if col_idx < len(level):
            return level[col_idx].get('value', '')
        
        return '' 