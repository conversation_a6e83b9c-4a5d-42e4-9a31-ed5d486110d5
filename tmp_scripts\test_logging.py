"""
测试日志配置的脚本
"""

from app import create_app
import logging

def test_logging():
    app = create_app()
    
    with app.app_context():
        # 测试不同级别的日志
        app.logger.info("这是一条信息日志")
        app.logger.warning("这是一条警告日志")
        app.logger.error("这是一条错误日志")
        
        # 测试其他模块的日志
        logger = logging.getLogger('app.routes.salary')
        logger.info("这是来自 salary 模块的日志")
        logger.debug("这是一条调试日志（可能不会显示）")
        
        # 测试异常日志
        try:
            1 / 0
        except Exception as e:
            app.logger.error(f"测试异常日志: {str(e)}", exc_info=True)
        
        print("日志测试完成，请检查控制台输出和 logs/ 目录下的文件")

if __name__ == '__main__':
    test_logging() 