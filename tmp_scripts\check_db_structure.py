"""
检查数据库表结构的脚本 - SQLite版本
"""

from app import create_app, db
from sqlalchemy import text

def check_db():
    app = create_app()
    
    with app.app_context():
        print("检查SQLite数据库表结构...")
        
        try:
            # 检查excel_template表是否存在 (SQLite方式)
            result = db.session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='excel_template'
            """)).fetchone()
            
            if result:
                print("✓ excel_template表存在")
                
                # 使用PRAGMA获取表的列结构 (SQLite方式)
                columns = db.session.execute(text("PRAGMA table_info(excel_template)")).fetchall()
                
                print("当前表结构:")
                for col in columns:
                    # SQLite PRAGMA table_info返回: (cid, name, type, notnull, dflt_value, pk)
                    col_name = col[1]
                    col_type = col[2]
                    not_null = col[3]
                    is_pk = col[5]
                    pk_info = " (PRIMARY KEY)" if is_pk else ""
                    null_info = "NOT NULL" if not_null else "NULL"
                    print(f"  - {col_name} ({col_type}) {null_info}{pk_info}")
                    
                # 检查是否有sheet_name字段
                sheet_name_exists = any(col[1] == 'sheet_name' for col in columns)
                if sheet_name_exists:
                    print("✓ sheet_name字段已存在")
                else:
                    print("✗ sheet_name字段不存在，需要添加")
                    
                # 检查表中现有数据
                count_result = db.session.execute(text("SELECT COUNT(*) FROM excel_template")).fetchone()
                print(f"表中现有记录数: {count_result[0]}")
                    
            else:
                print("✗ excel_template表不存在")
                
                # 检查是否有其他相关表
                all_tables = db.session.execute(text("""
                    SELECT name FROM sqlite_master WHERE type='table'
                """)).fetchall()
                print("数据库中现有表:")
                for table in all_tables:
                    print(f"  - {table[0]}")
                
        except Exception as e:
            print(f"✗ 检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_db() 