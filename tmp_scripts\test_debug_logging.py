"""
测试DEBUG日志级别
"""

from app import create_app
import logging

def test_debug_logging():
    app = create_app()
    
    with app.app_context():
        logger = logging.getLogger('app.routes.salary')
        
        print(f"当前日志级别: {logging.getLevelName(logger.level)}")
        print(f"根日志级别: {logging.getLevelName(logging.getLogger().level)}")
        
        # 测试各个级别的日志
        logger.debug("这是DEBUG级别的日志")
        logger.info("这是INFO级别的日志")
        logger.warning("这是WARNING级别的日志")
        logger.error("这是ERROR级别的日志")
        
        print("检查控制台和日志文件，看是否有DEBUG级别的输出")

if __name__ == '__main__':
    test_debug_logging() 