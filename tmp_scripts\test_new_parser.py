"""测试新的Excel解析器"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.excel_parser_v2 import ExcelParserV2
import json

def test_parser(file_path):
    """测试解析器"""
    print(f"测试文件: {file_path}")
    
    try:
        parser = ExcelParserV2(file_path, header_rows=4)
        result = parser.parse()
        
        print("\n=== 解析结果 ===")
        print(f"总记录数: {result['total_records']}")
        print(f"Sheet数量: {len(result['sheets'])}")
        
        for sheet_name, sheet_data in result['sheets'].items():
            print(f"\n--- Sheet: {sheet_name} ---")
            if 'error' in sheet_data:
                print(f"错误: {sheet_data['error']}")
                continue
                
            print(f"数据行数: {sheet_data['row_count']}")
            
            # 显示表头结构
            header_structure = sheet_data['header_structure']
            print(f"表头行数: {header_structure['total_rows']}")
            print(f"表头列数: {header_structure['total_cols']}")
            print(f"合并单元格数: {len(header_structure['merged_cells'])}")
            
            # 显示合并单元格信息
            for merged in header_structure['merged_cells']:
                print(f"  合并单元格: 行{merged['row']}-{merged['row']+merged['rowspan']-1}, "
                     f"列{merged['col']}-{merged['col']+merged['colspan']-1} "
                     f"({merged['rowspan']}x{merged['colspan']})")
            
            # 显示列映射
            print("列映射:")
            for col_idx, col_name in header_structure['column_mapping'].items():
                print(f"  列{col_idx}: {col_name}")
            
            # 显示前3行数据示例
            if sheet_data['data']:
                print("数据示例 (前3行):")
                for i, row in enumerate(sheet_data['data'][:3]):
                    print(f"  行{i+1}: {dict(list(row.items())[:5])}...")  # 只显示前5个字段
        
    except Exception as e:
        print(f"解析失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 替换为你的测试文件路径
    test_file = "test_salary.xlsx"  # 请替换为实际的Excel文件路径
    
    if len(sys.argv) > 1:
        test_file = sys.argv[1]
    
    if os.path.exists(test_file):
        test_parser(test_file)
    else:
        print(f"测试文件不存在: {test_file}")
        print("用法: python test_new_parser.py <excel_file_path>") 