"""
使用PyInstaller打包应用为exe文件
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('app/templates', 'app/templates'),
        ('app/static', 'app/static'),
    ],
    hiddenimports=[
        'app',
        'app.models',
        'app.models.salary',
        'app.models.sheet_template',
        'app.routes',
        'app.routes.main',
        'app.routes.salary',
        'app.utils',
        'app.utils.excel_parser_v2',
        'flask',
        'flask_sqlalchemy',
        'sqlalchemy',
        'openpyxl',
        'pandas',
        'werkzeug',
        'jinja2',
        'markupsafe',
        'click',
        'itsdangerous',
        'urllib3',
        'certifi',
        'charset_normalizer',
        'idna',
        'requests'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SalaryManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'  # 如果有图标文件的话
)
'''
    
    with open('SalaryManager.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("已创建PyInstaller规格文件: SalaryManager.spec")

def install_requirements():
    """安装必要的依赖"""
    requirements = [
        'Flask>=2.0.0',
        'Flask-SQLAlchemy>=3.0.0',
        'openpyxl>=3.0.0',
        'pandas>=1.3.0',
        'PyInstaller>=5.0.0',
        'Werkzeug>=2.0.0'
    ]
    
    print("正在安装依赖包...")
    for req in requirements:
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', req], check=True)
            print(f"✓ {req}")
        except subprocess.CalledProcessError:
            print(f"✗ {req} - 安装失败")
            return False
    
    return True

def build_exe():
    """构建exe文件"""
    try:
        print("开始构建exe文件...")
        
        # 使用PyInstaller构建
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            'SalaryManager.spec',
            '--clean',
            '--noconfirm'
        ]
        
        subprocess.run(cmd, check=True)
        print("✓ exe文件构建完成")
        
        # 检查生成的文件
        exe_path = Path('dist/SalaryManager.exe')
        if exe_path.exists():
            print(f"✓ exe文件位置: {exe_path.absolute()}")
            print(f"✓ 文件大小: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
            return True
        else:
            print("✗ 未找到生成的exe文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False

def create_readme():
    """创建使用说明"""
    readme_content = """
# 教职工工资管理系统

## 使用说明

1. 双击 `SalaryManager.exe` 启动程序
2. 程序会自动打开控制台窗口显示启动信息
3. 系统会自动通过默认浏览器打开管理界面
4. 在控制台窗口按 Ctrl+C 可以停止服务

## 功能特性

- 支持Excel工资表导入
- 支持复杂表头结构解析
- 提供数据查询和导出功能
- 支持中学和小学数据分类管理
- 提供数据清空功能

## 数据存储

- 程序运行时会在exe同目录下创建以下文件夹：
  - `data/` - 数据库文件存储
  - `uploads/` - 临时文件存储
  - `instance/` - 应用实例文件
  - `logs/` - 日志文件存储

## 注意事项

- 首次运行时会自动创建数据库
- 请确保exe有写入权限
- 关闭程序时请使用 Ctrl+C 或关闭控制台窗口

## 技术支持

如有问题请联系系统管理员。
"""
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content.strip())
    
    print("✓ 已创建使用说明文件")

def main():
    """主函数"""
    print("=" * 60)
    print("        教职工工资管理系统 - 打包工具")
    print("=" * 60)
    print()
    
    try:
        # 1. 安装依赖
        print("1. 检查并安装依赖...")
        if not install_requirements():
            print("✗ 依赖安装失败")
            return
        
        # 2. 创建规格文件
        print("\n2. 创建PyInstaller规格文件...")
        create_spec_file()
        
        # 3. 构建exe
        print("\n3. 构建exe文件...")
        if not build_exe():
            print("✗ 构建失败")
            return
        
        # 4. 创建说明文件
        print("\n4. 创建使用说明...")
        create_readme()
        
        print("\n" + "=" * 60)
        print("构建完成！")
        print("exe文件位置: dist/SalaryManager.exe")
        print("使用说明: dist/README.txt")
        print("=" * 60)
        
    except Exception as e:
        print(f"构建过程出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
