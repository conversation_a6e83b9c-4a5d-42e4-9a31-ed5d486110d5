"""Initial migration

Revision ID: 0a41172c3cfd
Revises: 
Create Date: 2025-05-21 18:58:26.929650

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0a41172c3cfd'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('employee',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.String(length=20), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('school_type', sa.String(length=20), nullable=False),
    sa.Column('department', sa.String(length=50), nullable=True),
    sa.Column('position', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('employee', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_employee_employee_id'), ['employee_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_employee_name'), ['name'], unique=False)

    op.create_table('salary_record',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('employee_id', sa.Integer(), nullable=False),
    sa.Column('salary_date', sa.Date(), nullable=False),
    sa.Column('base_salary', sa.Float(), nullable=True),
    sa.Column('position_salary', sa.Float(), nullable=True),
    sa.Column('seniority_salary', sa.Float(), nullable=True),
    sa.Column('performance_salary', sa.Float(), nullable=True),
    sa.Column('teaching_allowance', sa.Float(), nullable=True),
    sa.Column('class_management_allowance', sa.Float(), nullable=True),
    sa.Column('overtime_allowance', sa.Float(), nullable=True),
    sa.Column('other_allowance', sa.Float(), nullable=True),
    sa.Column('insurance_deduction', sa.Float(), nullable=True),
    sa.Column('fund_deduction', sa.Float(), nullable=True),
    sa.Column('tax_deduction', sa.Float(), nullable=True),
    sa.Column('other_deduction', sa.Float(), nullable=True),
    sa.Column('total_salary', sa.Float(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('source_file', sa.String(length=255), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employee.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('salary_record', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_salary_record_salary_date'), ['salary_date'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('salary_record', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_salary_record_salary_date'))

    op.drop_table('salary_record')
    with op.batch_alter_table('employee', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_employee_name'))
        batch_op.drop_index(batch_op.f('ix_employee_employee_id'))

    op.drop_table('employee')
    # ### end Alembic commands ### 