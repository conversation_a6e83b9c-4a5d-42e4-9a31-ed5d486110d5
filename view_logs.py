"""
日志文件查看工具
"""

import os
import sys
from datetime import datetime

def view_logs():
    """查看日志文件"""
    log_dir = 'logs'
    
    if not os.path.exists(log_dir):
        print("日志目录不存在")
        return
    
    log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
    
    if not log_files:
        print("没有找到日志文件")
        return
    
    print("可用的日志文件:")
    for i, file in enumerate(log_files, 1):
        file_path = os.path.join(log_dir, file)
        size = os.path.getsize(file_path)
        mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
        print(f"{i}. {file} ({size} 字节, 修改时间: {mtime})")
    
    try:
        choice = input("\n请选择要查看的文件编号 (按回车查看最新的): ").strip()
        
        if choice == "":
            # 选择最新的文件
            latest_file = max(log_files, key=lambda f: os.path.getmtime(os.path.join(log_dir, f)))
            selected_file = latest_file
        else:
            selected_file = log_files[int(choice) - 1]
        
        file_path = os.path.join(log_dir, selected_file)
        
        print(f"\n=== 查看文件: {selected_file} ===")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
            # 显示最后50行
            start_line = max(0, len(lines) - 50)
            
            print(f"显示最后 {len(lines) - start_line} 行 (共 {len(lines)} 行):")
            print("-" * 80)
            
            for i, line in enumerate(lines[start_line:], start_line + 1):
                print(f"{i:4d}: {line.rstrip()}")
                
    except (ValueError, IndexError):
        print("无效的选择")
    except UnicodeDecodeError as e:
        print(f"读取文件时编码错误: {e}")
        print("尝试用其他编码读取...")
        
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
                print("使用 GBK 编码读取成功")
                print(content[-1000:])  # 显示最后1000个字符
        except:
            print("无法读取文件，可能文件损坏")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")

if __name__ == '__main__':
    view_logs() 