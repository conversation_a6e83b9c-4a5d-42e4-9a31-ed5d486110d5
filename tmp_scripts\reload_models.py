"""
强制重新加载模型定义的脚本
"""

from app import create_app, db
from sqlalchemy import text

def reload_models():
    app = create_app()
    
    with app.app_context():
        print("重新加载模型...")
        
        try:
            # 检查数据库表结构
            print("检查数据库表结构:")
            columns = db.session.execute(text("PRAGMA table_info(excel_template)")).fetchall()
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
            
            # 重新导入模型
            from app.models.excel_template import ExcelTemplate
            
            # 测试模型是否正确加载
            print("\n测试模型定义...")
            print(f"ExcelTemplate 属性: {[attr for attr in dir(ExcelTemplate) if not attr.startswith('_')]}")
            
            # 测试查询
            print("\n测试数据库查询...")
            templates = ExcelTemplate.query.all()
            print(f"当前模板数量: {len(templates)}")
            
            for template in templates:
                print(f"  模板ID: {template.id}, 名称: {template.template_name}")
                if hasattr(template, 'sheet_name'):
                    print(f"    Sheet名称: {template.sheet_name}")
                else:
                    print("    ⚠️ 模型没有 sheet_name 属性")
            
            print("✓ 模型重新加载完成")
            
        except Exception as e:
            print(f"✗ 重新加载失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    reload_models() 