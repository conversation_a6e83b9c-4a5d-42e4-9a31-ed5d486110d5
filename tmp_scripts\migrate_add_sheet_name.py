"""
为ExcelTemplate表添加sheet_name字段的数据库迁移脚本 - SQLite版本
"""

from app import create_app, db
from sqlalchemy import text
import sys

def migrate():
    app = create_app()
    
    with app.app_context():
        print("开始迁移ExcelTemplate表（SQLite）...")
        
        try:
            # 首先检查表是否存在 (SQLite方式)
            table_exists = db.session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='excel_template'
            """)).fetchone()
            
            if not table_exists:
                print("excel_template表不存在，正在创建...")
                # 创建完整的表结构
                db.session.execute(text("""
                    CREATE TABLE excel_template (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sheet_name VARCHAR(100) NOT NULL,
                        template_name VARCHAR(100) NOT NULL,
                        header_structure TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                
                # 创建索引
                db.session.execute(text("CREATE INDEX ix_excel_template_sheet_name ON excel_template(sheet_name)"))
                
                # 创建唯一约束
                db.session.execute(text("""
                    CREATE UNIQUE INDEX uk_sheet_template ON excel_template(sheet_name, template_name, school_type)
                """))
                
                print("✓ excel_template表创建成功")
            else:
                print("excel_template表已存在，检查字段...")
                
                # 使用PRAGMA检查是否已经有sheet_name列 (SQLite方式)
                columns = db.session.execute(text("PRAGMA table_info(excel_template)")).fetchall()
                column_names = [col[1] for col in columns]
                
                if 'sheet_name' in column_names:
                    print("sheet_name字段已存在，跳过添加")
                else:
                    print("添加sheet_name字段...")
                    
                    # SQLite的ALTER TABLE ADD COLUMN语法
                    db.session.execute(text("ALTER TABLE excel_template ADD COLUMN sheet_name VARCHAR(100)"))
                    print("✓ 添加sheet_name字段成功")
                    
                    # 创建索引
                    try:
                        db.session.execute(text("CREATE INDEX ix_excel_template_sheet_name ON excel_template(sheet_name)"))
                        print("✓ 创建索引成功")
                    except Exception as e:
                        print(f"创建索引失败（可能已存在）: {e}")
                    
                    # 更新现有记录，设置默认sheet_name
                    db.session.execute(text("UPDATE excel_template SET sheet_name = 'Sheet1' WHERE sheet_name IS NULL"))
                    print("✓ 更新现有记录成功")
                    
                    # 清理空值
                    db.session.execute(text("UPDATE excel_template SET sheet_name = 'Sheet1' WHERE sheet_name = ''"))
                    print("✓ 清理空值成功")
            
            db.session.commit()
            print("✓ 数据库迁移完成")
            
            # 验证迁移结果
            print("\n验证迁移结果:")
            columns = db.session.execute(text("PRAGMA table_info(excel_template)")).fetchall()
            print("最终表结构:")
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                not_null = col[3]
                null_info = "NOT NULL" if not_null else "NULL"
                print(f"  - {col_name} ({col_type}) {null_info}")
            
        except Exception as e:
            db.session.rollback()
            print(f"✗ 迁移失败: {str(e)}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

if __name__ == '__main__':
    migrate() 