{% extends "base.html" %}

{% block title %}查询工资{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-800 mb-8">查询工资</h1>
    
    <!-- 查询条件表单 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- 姓名autocomplete组件 -->
            <div>
                <label class="block text-gray-700 text-sm font-bold mb-2">姓名</label>
                <div class="relative">
                    <div id="employee-autocomplete" class="w-full border border-gray-300 rounded-lg bg-white min-h-[42px] focus-within:ring-2 focus-within:ring-blue-500">
                        <!-- 选中的姓名标签 -->
                        <div id="employee-tags" class="flex flex-wrap gap-1 p-2"></div>
                        <!-- 输入框 -->
                        <input type="text" id="employee-input" placeholder="输入姓名搜索..." 
                               class="w-full px-2 py-1 border-0 outline-none text-sm" 
                               autocomplete="off">
                        <!-- 下拉列表 -->
                        <div id="employee-dropdown" class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto hidden">
                            <!-- 选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sheet名称树状组件 -->
            <div>
                <label class="block text-gray-700 text-sm font-bold mb-2">Sheet名称</label>
                <div class="relative">
                    <div id="sheet-tree-display" class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white cursor-pointer flex items-center justify-between min-h-[42px]">
                        <span class="display-text text-gray-500">选择Sheet...</span>
                        <span class="arrow">▼</span>
                    </div>
                    <div id="sheet-tree-dropdown" class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto hidden">
                        <!-- 树状结构将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
            
            <!-- 工资周期树状组件 -->
            <div>
                <label class="block text-gray-700 text-sm font-bold mb-2">工资周期</label>
                <div class="relative">
                    <div id="period-tree-display" class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white cursor-pointer flex items-center justify-between min-h-[42px]">
                        <span class="display-text text-gray-500">选择工资周期...</span>
                        <span class="arrow">▼</span>
                    </div>
                    <div id="period-tree-dropdown" class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto hidden">
                        <!-- 树状结构将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors mr-3">
                    查询
                </button>
                <button type="button" onclick="resetForm()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors mr-3">
                    重置
                </button>
                <button type="button" onclick="exportData()" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors mr-3">
                    导出Excel
                </button>
            </div>
        </form>
    </div>

    <!-- Tab切换 -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <button id="high-tab" onclick="switchTab('high')" 
                        class="tab-button active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    中学 <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">{{ total_high }}</span>
                </button>
                <button id="primary-tab" onclick="switchTab('primary')" 
                        class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    小学 <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">{{ total_primary }}</span>
                </button>
            </nav>
        </div>
    </div>

    <!-- 中学数据 -->
    <div id="high-content" class="tab-content">
        {% if high_school_data %}
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="flex min-h-[600px]">
                <!-- 左侧Sheet列表 -->
                <div class="w-64 bg-gray-50 border-r border-gray-200">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="font-semibold text-gray-900">Sheet列表</h3>
                    </div>
                    <div class="p-2">
                        {% for sheet_name, sheet_data in high_school_data.items() %}
                        <button onclick="loadSheetData('high', '{{ sheet_name }}', 1)" 
                                class="sheet-button w-full text-left p-3 mb-2 rounded-lg hover:bg-blue-50 transition-colors {% if loop.first %}bg-blue-100 border-l-4 border-blue-500{% else %}bg-white{% endif %}"
                                data-tab="high" data-sheet="{{ sheet_name }}">
                            <div class="font-medium text-gray-900">{{ sheet_name }}</div>
                            <div class="text-sm text-gray-500">{{ sheet_data.total_count }} 条记录</div>
                        </button>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- 右侧数据表格 -->
                <div class="flex-1 p-6 overflow-hidden">
                    <div id="high-data-container">
                        <!-- 数据内容将通过AJAX加载 -->
                        <div class="flex items-center justify-center h-64">
                            <div class="text-gray-500">请选择左侧的Sheet查看数据</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="bg-white rounded-lg shadow-md p-8 text-center text-gray-500">
            暂无中学数据
        </div>
        {% endif %}
    </div>

    <!-- 小学数据 -->
    <div id="primary-content" class="tab-content hidden">
        {% if primary_school_data %}
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="flex min-h-[600px]">
                <!-- 左侧Sheet列表 -->
                <div class="w-64 bg-gray-50 border-r border-gray-200">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="font-semibold text-gray-900">Sheet列表</h3>
                    </div>
                    <div class="p-2">
                        {% for sheet_name, sheet_data in primary_school_data.items() %}
                        <button onclick="loadSheetData('primary', '{{ sheet_name }}', 1)" 
                                class="sheet-button w-full text-left p-3 mb-2 rounded-lg hover:bg-green-50 transition-colors {% if loop.first %}bg-green-100 border-l-4 border-green-500{% else %}bg-white{% endif %}"
                                data-tab="primary" data-sheet="{{ sheet_name }}">
                            <div class="font-medium text-gray-900">{{ sheet_name }}</div>
                            <div class="text-sm text-gray-500">{{ sheet_data.total_count }} 条记录</div>
                        </button>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- 右侧数据表格 -->
                <div class="flex-1 p-6 overflow-hidden">
                    <div id="primary-data-container">
                        <!-- 数据内容将通过AJAX加载 -->
                        <div class="flex items-center justify-center h-64">
                            <div class="text-gray-500">请选择左侧的Sheet查看数据</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="bg-white rounded-lg shadow-md p-8 text-center text-gray-500">
            暂无小学数据
        </div>
        {% endif %}
    </div>
</div>

<!-- 详情弹窗 -->
<div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
            <div class="flex justify-between items-center p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">工资详情</h3>
                <button onclick="closeDetail()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div id="detailContent" class="p-6 overflow-y-auto max-h-[60vh]">
                <!-- 详情内容将通过JavaScript加载 -->
            </div>
        </div>
    </div>
</div>

<script>
// 引用base.html中的DOMUtils工具函数
// 如果DOMUtils不存在，创建一个简化版本
if (typeof DOMUtils === 'undefined') {
    window.DOMUtils = {
        safeGetElement: function(id) {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`Element with id '${id}' not found`);
            }
            return element;
        },
        safeSetHTML: function(id, html) {
            const element = this.safeGetElement(id);
            if (element) {
                element.innerHTML = html;
                return true;
            }
            return false;
        },
        safeToggleClass: function(id, className, add = null) {
            const element = this.safeGetElement(id);
            if (element) {
                if (add === true) {
                    element.classList.add(className);
                } else if (add === false) {
                    element.classList.remove(className);
                } else {
                    element.classList.toggle(className);
                }
                return true;
            }
            return false;
        }
    };
}

// 全局错误处理函数
function handleDOMError(operation, elementId, error) {
    console.error(`DOM操作失败 - ${operation}:`, {
        elementId: elementId,
        error: error.message,
        stack: error.stack
    });

    // 显示用户友好的错误提示
    showErrorNotification(`页面操作失败，请刷新页面重试`);
}

// 安全的DOM操作包装器
function safeDOMOperation(operation, elementId, callback) {
    try {
        return callback();
    } catch (error) {
        handleDOMError(operation, elementId, error);
        return null;
    }
}

// 显示错误通知
function showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm';
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <div>
                <div class="font-medium">操作失败</div>
                <div class="text-sm opacity-90">${message}</div>
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 5000);
}

// 全局变量
let currentTab = 'high';
let currentSheet = '';
let currentPage = 1;
let currentPerPage = 50;
let allOptions = {};
let currentSearchParams = {};

// 选中的值
let selectedEmployees = new Set();
let selectedSheets = new Set();
let selectedPeriods = new Set();

// 初始化所有组件
function initializeComponents() {
    // 加载所有选项数据
    fetch('/salary/api/options')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('加载选项失败:', data.error);
                return;
            }
            
            allOptions = data;
            console.log('加载的选项数据:', allOptions); // 调试日志
            
            // 初始化各个组件
            if (data.employees && data.employees.length > 0) {
                initEmployeeAutocomplete(data.employees);
            } else {
                console.warn('没有员工数据');
            }
            
            if (data.sheets && data.sheets.length > 0) {
                initSheetTreeSelect(data.sheets);
            } else {
                console.warn('没有Sheet数据');
            }
            
            if (data.periods && data.periods.length > 0) {
                initPeriodTreeSelect(data.periods);
            } else {
                console.warn('没有周期数据');
            }
            
            // 设置当前选中的值
            setTimeout(() => {
                setCurrentValues();
            }, 100);
        })
        .catch(error => {
            console.error('加载选项失败:', error);
            // 即使加载失败，也要初始化空组件
            allOptions = { employees: [], sheets: [], periods: [] };
            initEmployeeAutocomplete([]);
            initSheetTreeSelect([]);
            initPeriodTreeSelect([]);
        });
}

// 1. 姓名autocomplete组件
function initEmployeeAutocomplete(employees) {
    const input = DOMUtils.safeGetElement('employee-input');
    const dropdown = DOMUtils.safeGetElement('employee-dropdown');
    const tagsContainer = DOMUtils.safeGetElement('employee-tags');

    // 检查必要元素是否存在
    if (!input || !dropdown || !tagsContainer) {
        console.error('Employee autocomplete: Required elements not found');
        return;
    }

    // 初始化时确保标签容器为空
    tagsContainer.innerHTML = '';
    
    // 创建选项列表
    function createEmployeeOptions(filterText = '') {
        const filtered = employees.filter(emp => 
            emp.toLowerCase().includes(filterText.toLowerCase()) && 
            !selectedEmployees.has(emp)
        );
        
        dropdown.innerHTML = '';
        
        if (filtered.length === 0) {
            dropdown.innerHTML = '<div class="px-3 py-2 text-gray-500 text-sm">无匹配结果</div>';
            return;
        }
        
        filtered.forEach(employee => {
            const item = document.createElement('div');
            item.className = 'px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm';
            item.textContent = employee;
            item.onclick = () => selectEmployee(employee);
            dropdown.appendChild(item);
        });
    }
    
    // 选择姓名
    function selectEmployee(employee) {
        selectedEmployees.add(employee);
        updateEmployeeTags();
        input.value = '';
        dropdown.classList.add('hidden');
        createEmployeeOptions(); // 刷新列表，移除已选项
    }
    
    // 移除姓名
    function removeEmployee(employee) {
        selectedEmployees.delete(employee);
        updateEmployeeTags();
        createEmployeeOptions(input.value); // 刷新列表
    }
    
    // 更新标签显示
    function updateEmployeeTags() {
        tagsContainer.innerHTML = '';
        selectedEmployees.forEach(employee => {
            const tag = document.createElement('div');
            tag.className = 'inline-flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full';
            tag.innerHTML = `
                <span>${employee}</span>
                <button type="button" class="ml-1 text-blue-600 hover:text-blue-800" onclick="removeEmployee('${employee}')">×</button>
            `;
            tagsContainer.appendChild(tag);
        });
        
        // 如果有选中项，隐藏placeholder
        if (selectedEmployees.size > 0) {
            input.placeholder = '';
        } else {
            input.placeholder = '输入姓名搜索...';
        }
    }
    
    // 全局函数供标签调用
    window.removeEmployee = removeEmployee;
    
    // 输入事件
    input.addEventListener('input', (e) => {
        const value = e.target.value;
        createEmployeeOptions(value);
        if (value) {
            dropdown.classList.remove('hidden');
        } else {
            dropdown.classList.add('hidden');
        }
    });
    
    // 焦点事件
    input.addEventListener('focus', () => {
        createEmployeeOptions(input.value);
        dropdown.classList.remove('hidden');
    });
    
    // 点击外部关闭
    document.addEventListener('click', (e) => {
        if (!document.getElementById('employee-autocomplete').contains(e.target)) {
            dropdown.classList.add('hidden');
        }
    });
    
    // 初始化时更新显示
    updateEmployeeTags();
    createEmployeeOptions();
}

// 2. Sheet树状选择组件 - 修复父节点更新逻辑
function initSheetTreeSelect(sheets) {
    const display = document.getElementById('sheet-tree-display');
    const dropdown = document.getElementById('sheet-tree-dropdown');
    
    // 重置显示状态
    const displaySpan = display.querySelector('.display-text');
    if (displaySpan) {
        displaySpan.textContent = '选择Sheet...';
        displaySpan.className = 'display-text text-gray-500';
    }
    
    // 清空下拉列表
    dropdown.innerHTML = '';
    
    // 移除旧的事件监听器 - 使用克隆节点的方式
    const newDisplay = display.cloneNode(true);
    display.parentNode.replaceChild(newDisplay, display);
    
    // 重新获取元素引用（因为已经被替换）
    const newDisplayElement = document.getElementById('sheet-tree-display');
    const newDropdownElement = document.getElementById('sheet-tree-dropdown');
    
    if (!sheets || sheets.length === 0) {
        newDropdownElement.innerHTML = '<div class="px-3 py-2 text-gray-500 text-sm">暂无Sheet数据</div>';
        return;
    }
    
    // 按学校类型分组
    const groupedSheets = {
        high: sheets.filter(s => s.school_type === 'high'),
        primary: sheets.filter(s => s.school_type === 'primary')
    };
    
    // 创建树状结构
    function createSheetTree() {
        newDropdownElement.innerHTML = '';
        
        // 中学组
        if (groupedSheets.high.length > 0) {
            const highGroup = createSheetTreeGroup('中学', 'high', groupedSheets.high);
            newDropdownElement.appendChild(highGroup);
        }
        
        // 小学组
        if (groupedSheets.primary.length > 0) {
            const primaryGroup = createSheetTreeGroup('小学', 'primary', groupedSheets.primary);
            newDropdownElement.appendChild(primaryGroup);
        }
    }
    
    // 创建Sheet树状组 - 修复事件处理
    function createSheetTreeGroup(title, type, items) {
        const group = document.createElement('div');
        group.className = 'border-b border-gray-200 last:border-b-0';
        
        // 父节点
        const parent = document.createElement('div');
        parent.className = 'px-3 py-2 bg-gray-50 font-medium text-sm flex items-center justify-between cursor-pointer hover:bg-gray-100';
        
        // 计算选中状态
        const selectedCount = items.filter(item => selectedSheets.has(item.name)).length;
        const allSelected = selectedCount === items.length && items.length > 0;
        const partialSelected = selectedCount > 0 && selectedCount < items.length;
        
        parent.innerHTML = `
            <div class="flex items-center">
                <input type="checkbox" class="mr-2 parent-checkbox" ${allSelected ? 'checked' : ''} 
                       ${partialSelected ? 'style="opacity: 0.5"' : 'style="opacity: 1"'}>
                <span class="parent-label">${title} (${items.length})</span>
            </div>
            <span class="toggle-arrow">▼</span>
        `;
        
        // 子节点容器
        const children = document.createElement('div');
        children.className = 'sheet-children hidden';
        
        // 创建子节点
        items.forEach(item => {
            const child = document.createElement('div');
            child.className = 'px-6 py-2 text-sm flex items-center hover:bg-gray-50 cursor-pointer';
            child.innerHTML = `
                <input type="checkbox" class="mr-2 child-checkbox" value="${item.name}" 
                       ${selectedSheets.has(item.name) ? 'checked' : ''}>
                <span>${item.name}</span>
            `;
            
            const checkbox = child.querySelector('input');
            checkbox.addEventListener('change', () => {
                if (checkbox.checked) {
                    selectedSheets.add(item.name);
                } else {
                    selectedSheets.delete(item.name);
                }
                updateSheetDisplay();
                updateSheetParentState(parent, items);
            });
            
            children.appendChild(child);
        });
        
        // 父节点复选框事件
        const parentCheckbox = parent.querySelector('.parent-checkbox');
        parentCheckbox.addEventListener('change', () => {
            const checked = parentCheckbox.checked;
            
            // 更新所有子节点
            items.forEach(item => {
                if (checked) {
                    selectedSheets.add(item.name);
                } else {
                    selectedSheets.delete(item.name);
                }
            });
            
            // 更新子节点UI
            children.querySelectorAll('.child-checkbox').forEach(cb => {
                cb.checked = checked;
            });
            
            updateSheetDisplay();
            updateSheetParentState(parent, items);
        });
        
        // 折叠/展开事件
        parent.addEventListener('click', (e) => {
            if (!e.target.closest('input')) {
                children.classList.toggle('hidden');
                const arrow = parent.querySelector('.toggle-arrow');
                arrow.textContent = children.classList.contains('hidden') ? '▼' : '▲';
            }
        });
        
        group.appendChild(parent);
        group.appendChild(children);
        
        return group;
    }
    
    // 新的父节点状态更新函数
    function updateSheetParentState(parentElement, items) {
        const selectedCount = items.filter(item => selectedSheets.has(item.name)).length;
        const parentCheckbox = parentElement.querySelector('.parent-checkbox');
        const parentLabel = parentElement.querySelector('.parent-label');
        
        // 更新复选框状态
        parentCheckbox.checked = selectedCount === items.length && items.length > 0;
        
        // 设置半选状态样式
        if (selectedCount > 0 && selectedCount < items.length) {
            parentCheckbox.style.opacity = '0.5';
        } else {
            parentCheckbox.style.opacity = '1';
        }
        
        // 只显示总数，不显示已选数量
        const title = parentLabel.textContent.includes('中学') ? '中学' : '小学';
        parentLabel.textContent = `${title} (${items.length})`;
    }
    
    // 更新显示
    function updateSheetDisplay() {
        const displaySpan = newDisplayElement.querySelector('.display-text');
        if (selectedSheets.size === 0) {
            displaySpan.textContent = '选择Sheet...';
            displaySpan.className = 'display-text text-gray-500';
        } else {
            displaySpan.textContent = `已选择 ${selectedSheets.size} 个Sheet`;
            displaySpan.className = 'display-text text-gray-900';
        }
    }
    
    // 重新绑定事件监听器
    newDisplayElement.addEventListener('click', () => {
        newDropdownElement.classList.toggle('hidden');
    });
    
    // 点击外部关闭（移除旧的监听器并添加新的）
    document.removeEventListener('click', window.sheetOutsideClickHandler);
    window.sheetOutsideClickHandler = (e) => {
        if (!newDisplayElement.parentNode.contains(e.target)) {
            newDropdownElement.classList.add('hidden');
        }
    };
    document.addEventListener('click', window.sheetOutsideClickHandler);
    
    // 初始化
    createSheetTree();
    updateSheetDisplay();
}

// 3. 工资周期树状选择组件 - 修复父节点更新逻辑
function initPeriodTreeSelect(periods) {
    const display = document.getElementById('period-tree-display');
    const dropdown = document.getElementById('period-tree-dropdown');
    
    // 重置显示状态
    const displaySpan = display.querySelector('.display-text');
    if (displaySpan) {
        displaySpan.textContent = '选择工资周期...';
        displaySpan.className = 'display-text text-gray-500';
    }
    
    // 移除旧的事件监听器 - 使用克隆节点的方式
    const newDisplay = display.cloneNode(true);
    display.parentNode.replaceChild(newDisplay, display);
    
    // 重新获取元素引用
    const newDisplayElement = document.getElementById('period-tree-display');
    const newDropdownElement = document.getElementById('period-tree-dropdown');
    
    // 清空下拉列表
    newDropdownElement.innerHTML = '';
    
    if (!periods || periods.length === 0) {
        // 尝试从API获取分组数据
        loadPeriodsBySchoolType();
        return;
    }
    
    // 按学校类型分组周期
    const groupedPeriods = { high: [], primary: [] };
    
    // 加载按学校类型分组的周期数据
    function loadPeriodsBySchoolType() {
        fetch('/salary/api/periods_by_school_type')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('获取分组周期失败:', data.error);
                    fallbackCreatePeriodTree(periods || []);
                    return;
                }
                
                groupedPeriods.high = data.high || [];
                groupedPeriods.primary = data.primary || [];
                createPeriodTree();
            })
            .catch(error => {
                console.error('获取分组周期失败:', error);
                fallbackCreatePeriodTree(periods || []);
            });
    }
    
    // 创建树状结构
    function createPeriodTree() {
        newDropdownElement.innerHTML = '';
        
        // 中学组
        if (groupedPeriods.high.length > 0) {
            const highGroup = createPeriodTreeGroup('中学', 'high', groupedPeriods.high);
            newDropdownElement.appendChild(highGroup);
        }
        
        // 小学组
        if (groupedPeriods.primary.length > 0) {
            const primaryGroup = createPeriodTreeGroup('小学', 'primary', groupedPeriods.primary);
            newDropdownElement.appendChild(primaryGroup);
        }
    }
    
    // 创建周期树状组 - 修复事件处理
    function createPeriodTreeGroup(title, type, items) {
        const group = document.createElement('div');
        group.className = 'border-b border-gray-200 last:border-b-0';
        
        // 父节点
        const parent = document.createElement('div');
        parent.className = 'px-3 py-2 bg-gray-50 font-medium text-sm flex items-center justify-between cursor-pointer hover:bg-gray-100';
        
        // 计算选中状态
        const selectedCount = items.filter(item => selectedPeriods.has(item)).length;
        const allSelected = selectedCount === items.length && items.length > 0;
        const partialSelected = selectedCount > 0 && selectedCount < items.length;
        
        parent.innerHTML = `
            <div class="flex items-center">
                <input type="checkbox" class="mr-2 parent-checkbox" ${allSelected ? 'checked' : ''} 
                       ${partialSelected ? 'style="opacity: 0.5"' : 'style="opacity: 1"'}>
                <span class="parent-label">${title} (${items.length})</span>
            </div>
            <span class="toggle-arrow">▼</span>
        `;
        
        // 子节点容器
        const children = document.createElement('div');
        children.className = 'period-children hidden';
        
        // 创建子节点
        items.forEach(item => {
            const child = document.createElement('div');
            child.className = 'px-6 py-2 text-sm flex items-center hover:bg-gray-50 cursor-pointer';
            child.innerHTML = `
                <input type="checkbox" class="mr-2 child-checkbox" value="${item}" 
                       ${selectedPeriods.has(item) ? 'checked' : ''}>
                <span>${item}</span>
            `;
            
            const checkbox = child.querySelector('input');
            checkbox.addEventListener('change', () => {
                if (checkbox.checked) {
                    selectedPeriods.add(item);
                } else {
                    selectedPeriods.delete(item);
                }
                updatePeriodDisplay();
                updatePeriodParentState(parent, items);
            });
            
            children.appendChild(child);
        });
        
        // 父节点复选框事件
        const parentCheckbox = parent.querySelector('.parent-checkbox');
        parentCheckbox.addEventListener('change', () => {
            const checked = parentCheckbox.checked;
            
            // 更新所有子节点
            items.forEach(item => {
                if (checked) {
                    selectedPeriods.add(item);
                } else {
                    selectedPeriods.delete(item);
                }
            });
            
            // 更新子节点UI
            children.querySelectorAll('.child-checkbox').forEach(cb => {
                cb.checked = checked;
            });
            
            updatePeriodDisplay();
            updatePeriodParentState(parent, items);
        });
        
        // 折叠/展开事件
        parent.addEventListener('click', (e) => {
            if (!e.target.closest('input')) {
                children.classList.toggle('hidden');
                const arrow = parent.querySelector('.toggle-arrow');
                arrow.textContent = children.classList.contains('hidden') ? '▼' : '▲';
            }
        });
        
        group.appendChild(parent);
        group.appendChild(children);
        
        return group;
    }
    
    // 新的父节点状态更新函数
    function updatePeriodParentState(parentElement, items) {
        const selectedCount = items.filter(item => selectedPeriods.has(item)).length;
        const parentCheckbox = parentElement.querySelector('.parent-checkbox');
        const parentLabel = parentElement.querySelector('.parent-label');
        
        // 更新复选框状态
        parentCheckbox.checked = selectedCount === items.length && items.length > 0;
        
        // 设置半选状态样式
        if (selectedCount > 0 && selectedCount < items.length) {
            parentCheckbox.style.opacity = '0.5';
        } else {
            parentCheckbox.style.opacity = '1';
        }
        
        // 只显示总数，不显示已选数量
        const title = parentLabel.textContent.includes('中学') ? '中学' : '小学';
        parentLabel.textContent = `${title} (${items.length})`;
    }
    
    // 更新显示
    function updatePeriodDisplay() {
        const displaySpan = newDisplayElement.querySelector('.display-text');
        if (selectedPeriods.size === 0) {
            displaySpan.textContent = '选择工资周期...';
            displaySpan.className = 'display-text text-gray-500';
        } else {
            displaySpan.textContent = `已选择 ${selectedPeriods.size} 个周期`;
            displaySpan.className = 'display-text text-gray-900';
        }
    }
    
    // 备用方案：按年份分组 - 修复事件处理
    function fallbackCreatePeriodTree(periods) {
        newDropdownElement.innerHTML = '';
        
        // 按年份分组周期
        const groupedByYear = {};
        periods.forEach(period => {
            const yearMatch = period.match(/(\d{4})/);
            const year = yearMatch ? yearMatch[1] : '其他';
            
            if (!groupedByYear[year]) {
                groupedByYear[year] = [];
            }
            groupedByYear[year].push(period);
        });
        
        // 按年份排序
        const sortedYears = Object.keys(groupedByYear).sort((a, b) => {
            if (a === '其他') return 1;
            if (b === '其他') return -1;
            return b.localeCompare(a);
        });
        
        sortedYears.forEach(year => {
            const group = createFallbackPeriodGroup(year, groupedByYear[year]);
            newDropdownElement.appendChild(group);
        });
    }
    
    function createFallbackPeriodGroup(year, items) {
        const group = document.createElement('div');
        group.className = 'border-b border-gray-200 last:border-b-0';
        
        const parent = document.createElement('div');
        parent.className = 'px-3 py-2 bg-gray-50 font-medium text-sm flex items-center justify-between cursor-pointer hover:bg-gray-100';
        
        // 计算选中状态
        const selectedCount = items.filter(item => selectedPeriods.has(item)).length;
        const allSelected = selectedCount === items.length && items.length > 0;
        const partialSelected = selectedCount > 0 && selectedCount < items.length;
        
        parent.innerHTML = `
            <div class="flex items-center">
                <input type="checkbox" class="mr-2 parent-checkbox" ${allSelected ? 'checked' : ''} 
                       ${partialSelected ? 'style="opacity: 0.5"' : 'style="opacity: 1"'}>
                <span class="parent-label">${year}年 (${items.length})</span>
            </div>
            <span class="toggle-arrow">▼</span>
        `;
        
        const children = document.createElement('div');
        children.className = 'period-children hidden';
        
        items.forEach(item => {
            const child = document.createElement('div');
            child.className = 'px-6 py-2 text-sm flex items-center hover:bg-gray-50 cursor-pointer';
            child.innerHTML = `
                <input type="checkbox" class="mr-2 child-checkbox" value="${item}" 
                       ${selectedPeriods.has(item) ? 'checked' : ''}>
                <span>${item}</span>
            `;
            
            const checkbox = child.querySelector('input');
            checkbox.addEventListener('change', () => {
                if (checkbox.checked) {
                    selectedPeriods.add(item);
                } else {
                    selectedPeriods.delete(item);
                }
                updatePeriodDisplay();
                updateFallbackParentState(parent, items);
            });
            
            children.appendChild(child);
        });
        
        // 父节点复选框事件
        const parentCheckbox = parent.querySelector('.parent-checkbox');
        parentCheckbox.addEventListener('change', () => {
            const checked = parentCheckbox.checked;
            
            items.forEach(item => {
                if (checked) {
                    selectedPeriods.add(item);
                } else {
                    selectedPeriods.delete(item);
                }
            });
            
            // 更新子节点UI
            children.querySelectorAll('.child-checkbox').forEach(cb => {
                cb.checked = checked;
            });
            
            updatePeriodDisplay();
            updateFallbackParentState(parent, items);
        });
        
        // 折叠/展开事件
        parent.addEventListener('click', (e) => {
            if (!e.target.closest('input')) {
                children.classList.toggle('hidden');
                const arrow = parent.querySelector('.toggle-arrow');
                arrow.textContent = children.classList.contains('hidden') ? '▼' : '▲';
            }
        });
        
        group.appendChild(parent);
        group.appendChild(children);
        
        return group;
    }
    
    // 备用方案的父节点状态更新函数
    function updateFallbackParentState(parentElement, items) {
        const selectedCount = items.filter(item => selectedPeriods.has(item)).length;
        const parentCheckbox = parentElement.querySelector('.parent-checkbox');
        const parentLabel = parentElement.querySelector('.parent-label');
        
        // 更新复选框状态
        parentCheckbox.checked = selectedCount === items.length && items.length > 0;
        
        // 设置半选状态样式
        if (selectedCount > 0 && selectedCount < items.length) {
            parentCheckbox.style.opacity = '0.5';
        } else {
            parentCheckbox.style.opacity = '1';
        }
        
        // 只显示总数，不显示已选数量
        const year = parentLabel.textContent.split('年')[0];
        parentLabel.textContent = `${year}年 (${items.length})`;
    }
    
    // 重新绑定事件监听器
    newDisplayElement.addEventListener('click', () => {
        newDropdownElement.classList.toggle('hidden');
    });
    
    // 点击外部关闭（移除旧的监听器并添加新的）
    document.removeEventListener('click', window.periodOutsideClickHandler);
    window.periodOutsideClickHandler = (e) => {
        if (!newDisplayElement.parentNode.contains(e.target)) {
            newDropdownElement.classList.add('hidden');
        }
    };
    document.addEventListener('click', window.periodOutsideClickHandler);
    
    // 初始化
    loadPeriodsBySchoolType();
    updatePeriodDisplay();
}

// 获取搜索参数
function getSearchParams() {
    const params = {};
    
    if (selectedEmployees.size > 0) {
        params['employee_id'] = Array.from(selectedEmployees).join(',');
    }
    
    if (selectedSheets.size > 0) {
        params['search_sheet_name'] = Array.from(selectedSheets).join(',');
    }
    
    if (selectedPeriods.size > 0) {
        params['salary_period'] = Array.from(selectedPeriods).join(',');
    }
    
    return params;
}

// 修改表单提交事件 - 完全AJAX化
function handleFormSubmit(e) {
    e.preventDefault();
    
    const params = getSearchParams();
    currentSearchParams = params; // 保存搜索参数
    
    // 显示搜索加载状态
    showSearchLoading();
    
    // 构建查询URL
    const searchUrl = new URL('/salary/api/search', window.location.origin);
    Object.entries(params).forEach(([key, value]) => {
        if (value) {
            searchUrl.searchParams.set(key, value);
        }
    });
    
    // 发送AJAX搜索请求
    fetch(searchUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新页面内容
                updateSearchResults(data);
                
                // 更新浏览器URL（不刷新页面）
                updateBrowserURL(params);
            } else {
                showSearchError(data.error || '搜索失败');
            }
        })
        .catch(error => {
            console.error('搜索失败:', error);
            showSearchError('搜索请求失败：' + error.message);
        })
        .finally(() => {
            hideSearchLoading();
        });
}

// 显示搜索加载状态
function showSearchLoading() {
    // 更新tab按钮显示加载状态
    DOMUtils.safeSetHTML('high-tab', `
        中学 <span class="ml-2 px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">加载中...</span>
    `);
    DOMUtils.safeSetHTML('primary-tab', `
        小学 <span class="ml-2 px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">加载中...</span>
    `);

    // 清空数据区域，显示加载状态
    DOMUtils.safeSetHTML('high-data-container', `
        <div class="flex items-center justify-center h-64">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-gray-600">正在搜索...</span>
        </div>
    `);
    DOMUtils.safeSetHTML('primary-data-container', `
        <div class="flex items-center justify-center h-64">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <span class="ml-2 text-gray-600">正在搜索...</span>
        </div>
    `);
}

// 隐藏搜索加载状态
function hideSearchLoading() {
    // 加载状态会在 updateSearchResults 中被替换
}

// 更新搜索结果 - 只显示有匹配数据的sheet
function updateSearchResults(data) {
    console.log('更新搜索结果:', data);

    // 更新tab统计数字（显示匹配查询条件的记录总数）
    DOMUtils.safeSetHTML('high-tab', `
        中学 <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">${data.total_high}</span>
    `);
    DOMUtils.safeSetHTML('primary-tab', `
        小学 <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">${data.total_primary}</span>
    `);

    // 更新中学sheet列表（只显示有数据的sheet）
    updateSheetList('high', data.high_school_data);

    // 更新小学sheet列表（只显示有数据的sheet）
    updateSheetList('primary', data.primary_school_data);

    // 重置当前选中的sheet
    currentSheet = '';

    // 重置数据显示区域
    resetDataContainers();

    // 如果当前tab没有数据，显示相应提示
    if (currentTab === 'high' && Object.keys(data.high_school_data).length === 0) {
        DOMUtils.safeSetHTML('high-data-container', `
            <div class="flex items-center justify-center h-64">
                <div class="text-gray-500">中学暂无匹配的数据</div>
            </div>
        `);
    }

    if (currentTab === 'primary' && Object.keys(data.primary_school_data).length === 0) {
        DOMUtils.safeSetHTML('primary-data-container', `
            <div class="flex items-center justify-center h-64">
                <div class="text-gray-500">小学暂无匹配的数据</div>
            </div>
        `);
    }
}

// 更新sheet列表 - 只显示有匹配数据的sheet
function updateSheetList(tabName, sheetData) {
    return safeDOMOperation('updateSheetList', `${tabName}-content`, () => {
        const sheetListContainer = document.querySelector(`#${tabName}-content .p-2`);

        // 安全检查：确保容器元素存在
        if (!sheetListContainer) {
            console.error(`updateSheetList: Sheet list container not found for tab '${tabName}'`);
            return;
        }

        if (!sheetData || Object.keys(sheetData).length === 0) {
            sheetListContainer.innerHTML = `
                <div class="p-4 text-center text-gray-500">
                    暂无匹配的Sheet数据
                </div>
            `;
            return;
        }
    
    let html = '';
    let isFirst = true;
    
    // 只显示有数据的sheet
    Object.entries(sheetData).forEach(([sheetName, sheetInfo]) => {
        // 确保这个sheet有数据
        if (sheetInfo.total_count > 0) {
            const colorClass = tabName === 'high' ? 'blue' : 'green';
            const activeClass = isFirst ? `bg-${colorClass}-100 border-l-4 border-${colorClass}-500` : 'bg-white';
            
            html += `
                <button onclick="loadSheetData('${tabName}', '${sheetName}', 1)" 
                        class="sheet-button w-full text-left p-3 mb-2 rounded-lg hover:bg-${colorClass}-50 transition-colors ${activeClass}"
                        data-tab="${tabName}" data-sheet="${sheetName}">
                    <div class="font-medium text-gray-900">${sheetName}</div>
                    <div class="text-sm text-gray-500">${sheetInfo.total_count} 条记录</div>
                </button>
            `;
            isFirst = false;
        }
    });
    
    // 如果没有有效的sheet数据
    if (!html) {
        html = `
            <div class="p-4 text-center text-gray-500">
                暂无匹配的Sheet数据
            </div>
        `;
    }
    
        // 安全设置HTML内容
        if (sheetListContainer) {
            sheetListContainer.innerHTML = html;
        }
    });
}

// 重置数据显示区域
function resetDataContainers() {
    DOMUtils.safeSetHTML('high-data-container', `
        <div class="flex items-center justify-center h-64">
            <div class="text-gray-500">请选择左侧的Sheet查看数据</div>
        </div>
    `);
    DOMUtils.safeSetHTML('primary-data-container', `
        <div class="flex items-center justify-center h-64">
            <div class="text-gray-500">请选择左侧的Sheet查看数据</div>
        </div>
    `);
}

// 显示搜索错误
function showSearchError(errorMessage) {
    DOMUtils.safeSetHTML('high-data-container', `
        <div class="flex items-center justify-center h-64">
            <div class="text-red-600 text-center">搜索失败：${errorMessage}</div>
        </div>
    `);
    DOMUtils.safeSetHTML('primary-data-container', `
        <div class="flex items-center justify-center h-64">
            <div class="text-red-600 text-center">搜索失败：${errorMessage}</div>
        </div>
    `);
}

// 更新浏览器URL（不刷新页面）
function updateBrowserURL(params) {
    const url = new URL(window.location.pathname, window.location.origin);
    
    Object.entries(params).forEach(([key, value]) => {
        if (value) {
            url.searchParams.set(key, value);
        }
    });
    
    // 使用 history API 更新URL，但不刷新页面
    window.history.pushState({searchParams: params}, '', url.toString());
}

// 修改加载sheet数据函数，使用当前搜索参数
function loadSheetData(tabName, sheetName, page = 1) {
    currentTab = tabName;
    currentSheet = sheetName;
    currentPage = page;

    // 更新sheet按钮状态
    resetSheetButtons(tabName);
    const activeButton = document.querySelector(`[data-tab="${tabName}"][data-sheet="${sheetName}"]`);
    if (activeButton) {
        if (tabName === 'high') {
            activeButton.classList.add('bg-blue-100', 'border-l-4', 'border-blue-500');
        } else {
            activeButton.classList.add('bg-green-100', 'border-l-4', 'border-green-500');
        }
        activeButton.classList.remove('bg-white');
    }

    // 显示加载状态
    const container = DOMUtils.safeGetElement(tabName + '-data-container');
    if (container) {
        container.innerHTML = `
            <div class="flex items-center justify-center h-64">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span class="ml-2 text-gray-600">加载中...</span>
            </div>
        `;
    }
    
    // 构建API请求URL，使用当前搜索参数
    const apiUrl = new URL('/salary/api/sheet_data', window.location.origin);
    apiUrl.searchParams.set('sheet_name', sheetName);
    apiUrl.searchParams.set('school_type', tabName);
    apiUrl.searchParams.set('page', page);
    apiUrl.searchParams.set('per_page', currentPerPage);
    
    // 添加当前搜索条件
    Object.entries(currentSearchParams).forEach(([key, value]) => {
        if (value) {
            // 转换参数名
            if (key === 'search_sheet_name') {
                apiUrl.searchParams.set('search_sheet_name', value);
            } else {
                apiUrl.searchParams.set(key, value);
            }
        }
    });
    
    // 发送AJAX请求
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                if (container) {
                    container.innerHTML = `<div class="text-red-600 text-center p-8">${data.error}</div>`;
                }
                return;
            }

            renderSheetData(container, data, sheetName);
        })
        .catch(error => {
            console.error('加载数据失败:', error);
            if (container) {
                container.innerHTML = `<div class="text-red-600 text-center p-8">加载失败: ${error.message}</div>`;
            }
        });
}

// 渲染sheet数据
function renderSheetData(container, data, sheetName) {
    if (!container) {
        console.error('renderSheetData: container is null');
        return;
    }

    let html = `
        <div class="flex justify-between items-center mb-4">
            <h4 class="text-lg font-semibold text-gray-900">${sheetName}</h4>
            <span class="text-sm text-gray-500">共 ${data.pagination.total} 条记录</span>
        </div>

        <div class="overflow-auto border border-gray-200 rounded-lg" style="max-height: 60vh; max-width: 100%;">
    `;

    if (data.records && data.records.length > 0) {
        html += renderDataTable(data);
    } else {
        html += '<div class="p-8 text-center text-gray-500">该Sheet暂无数据</div>';
    }

    html += '</div>';

    // 添加分页器
    if (data.pagination.pages > 1) {
        html += renderPagination(data.pagination);
    }

    container.innerHTML = html;
}

// 渲染数据表格
function renderDataTable(data) {
    let html = `
        <table class="min-w-full divide-y divide-gray-200 border-collapse" style="width: max-content; border: 1px solid #e5e7eb;">
            <thead class="bg-gray-50 sticky top-0">
    `;
    
    // 解析表头结构（从JSON字符串转换为对象）
    let headerStructure = null;
    if (data.header_structure) {
        try {
            if (typeof data.header_structure === 'string') {
                headerStructure = JSON.parse(data.header_structure);
            } else {
                headerStructure = data.header_structure;
            }
            console.log('解析后的表头结构:', headerStructure); // 调试日志
        } catch (e) {
            console.error('表头结构解析失败:', e);
            headerStructure = null;
        }
    }
    
    // 渲染表头
    if (headerStructure && headerStructure.levels && headerStructure.levels.length > 0) {
        console.log('使用复杂表头渲染'); // 调试日志
        html += renderComplexHeader(headerStructure);
    } else {
        console.log('使用简单表头渲染'); // 调试日志
        html += renderSimpleHeader(data.records[0].data_array);
    }
    
    html += `
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
    `;
    
    // 渲染数据行
    data.records.forEach(record => {
        html += '<tr class="hover:bg-gray-50">';
        record.data_array.forEach(value => {
            const displayValue = isNumber(value) ? formatNumber(value) : (value || '');
            html += `
                <td class="px-3 py-2 text-sm" style="min-width: 100px; border: 1px solid #e5e7eb;">
                    <div class="truncate max-w-xs" title="${value || ''}">${displayValue}</div>
                </td>
            `;
        });
        html += `
            <td class="px-3 py-2 text-sm text-center" style="min-width: 120px; border: 1px solid #e5e7eb;">
                ${record.salary_period}
            </td>
        `;
        html += '</tr>';
    });
    
    html += '</tbody></table>';
    return html;
}

// 修复渲染复杂表头函数
function renderComplexHeader(headerStructure) {
    console.log('开始渲染复杂表头:', headerStructure); // 调试日志
    
    let html = '';
    const covered_cells = new Set(); // 使用Set来追踪被覆盖的单元格
    const headerRowCount = headerStructure.levels.length;
    
    // 预处理合并单元格信息，建立索引
    const mergedCellsMap = new Map();
    if (headerStructure.merged_cells) {
        headerStructure.merged_cells.forEach(mc => {
            const key = `${mc.row},${mc.col}`;
            mergedCellsMap.set(key, mc);
            
            // 标记所有被覆盖的单元格
            for (let r = mc.row; r < mc.row + mc.rowspan; r++) {
                for (let c = mc.col; c < mc.col + mc.colspan; c++) {
                    if (r !== mc.row || c !== mc.col) { // 不包括起始单元格本身
                        covered_cells.add(`${r},${c}`);
                    }
                }
            }
        });
    }
    
    console.log('合并单元格映射:', mergedCellsMap);
    console.log('被覆盖的单元格:', covered_cells);
    
    headerStructure.levels.forEach((level, levelIdx) => {
        html += '<tr>';
        
        level.forEach((cell, colIdx) => {
            const cellKey = `${levelIdx},${colIdx}`;
            
            // 跳过被覆盖的单元格
            if (covered_cells.has(cellKey)) {
                return;
            }
            
            // 查找合并信息
            const mergedInfo = mergedCellsMap.get(cellKey) || { colspan: 1, rowspan: 1 };
            
            console.log(`单元格[${levelIdx},${colIdx}]: "${cell.value || ''}", colspan=${mergedInfo.colspan}, rowspan=${mergedInfo.rowspan}`);
            
            html += `
                <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase bg-gray-50" 
                    style="min-width: 100px; border: 1px solid #d1d5db; font-weight: 600;"
                    ${mergedInfo.colspan > 1 ? `colspan="${mergedInfo.colspan}"` : ''}
                    ${mergedInfo.rowspan > 1 ? `rowspan="${mergedInfo.rowspan}"` : ''}>
                    ${cell.value || '&nbsp;'}
                </th>
            `;
        });
        
        // 在第一行添加工资周期列表头
        if (levelIdx === 0) {
            html += `
                <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase bg-gray-50" 
                    style="min-width: 120px; border: 1px solid #d1d5db; font-weight: 600;"
                    rowspan="${headerRowCount}">
                    工资周期
                </th>
            `;
        }
        
        html += '</tr>';
    });
    
    console.log('生成的表头HTML:', html); // 调试日志
    return html;
}

// 渲染简单表头
function renderSimpleHeader(dataArray) {
    let html = '<tr>';
    dataArray.forEach((_, index) => {
        html += `
            <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase" style="min-width: 100px; border: 1px solid #e5e7eb;">
                列${index + 1}
            </th>
        `;
    });
    html += `
        <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase" style="min-width: 120px; border: 1px solid #e5e7eb;">
            工资周期
        </th>
    `;
    html += '</tr>';
    return html;
}

// 渲染分页器
function renderPagination(pagination) {
    let html = `
        <div class="mt-4 flex items-center justify-between p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div class="text-sm text-gray-700">
                显示第 ${(pagination.page - 1) * pagination.per_page + 1} 到 
                ${Math.min(pagination.page * pagination.per_page, pagination.total)} 条，
                共 ${pagination.total} 条记录
            </div>
            
            <div class="flex items-center space-x-2">
    `;
    
    // 上一页按钮
    if (pagination.has_prev) {
        html += `
            <button onclick="loadSheetData('${currentTab}', '${currentSheet}', ${pagination.prev_num})" 
                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                上一页
            </button>
        `;
    } else {
        html += `
            <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
                上一页
            </span>
        `;
    }
    
    // 页码按钮
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);
    
    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        const isActive = pageNum === pagination.page;
        html += `
            <button onclick="loadSheetData('${currentTab}', '${currentSheet}', ${pageNum})" 
                    class="px-3 py-2 text-sm font-medium ${isActive ? 'text-blue-600 bg-blue-50 border-blue-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50'} border rounded-md">
                ${pageNum}
            </button>
        `;
    }
    
    // 下一页按钮
    if (pagination.has_next) {
        html += `
            <button onclick="loadSheetData('${currentTab}', '${currentSheet}', ${pagination.next_num})" 
                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                下一页
            </button>
        `;
    } else {
        html += `
            <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
                下一页
            </span>
        `;
    }
    
    html += '</div></div>';
    return html;
}

// 数字检测和格式化函数
function isNumber(value) {
    return !isNaN(parseFloat(value)) && isFinite(value);
}

function formatNumber(value) {
    const num = parseFloat(value);
    return num === parseInt(num) ? parseInt(num).toString() : num.toFixed(2);
}

// Tab切换功能
function switchTab(tabName) {
    currentTab = tabName;

    // 更新tab按钮状态
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
        btn.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
    });

    // 安全更新当前tab按钮状态
    const currentTabButton = DOMUtils.safeGetElement(tabName + '-tab');
    if (currentTabButton) {
        currentTabButton.classList.add('active', 'border-blue-500', 'text-blue-600');
        currentTabButton.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
    }

    // 显示对应内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    DOMUtils.safeToggleClass(tabName + '-content', 'hidden', false);

    // 重置数据容器
    DOMUtils.safeSetHTML(tabName + '-data-container',
        '<div class="flex items-center justify-center h-64"><div class="text-gray-500">请选择左侧的Sheet查看数据</div></div>'
    );

    // 重置sheet按钮状态
    resetSheetButtons(tabName);
}

// 重置sheet按钮状态
function resetSheetButtons(tabName) {
    const currentTabContent = DOMUtils.safeGetElement(tabName + '-content');
    if (currentTabContent) {
        currentTabContent.querySelectorAll('.sheet-button').forEach(btn => {
            btn.classList.remove('bg-blue-100', 'bg-green-100', 'border-l-4', 'border-blue-500', 'border-green-500');
            btn.classList.add('bg-white');
        });
    }
}

// 重置表单
function resetForm() {
    // 清空选中的值
    selectedEmployees.clear();
    selectedSheets.clear();
    selectedPeriods.clear();
    
    // 手动清空姓名标签显示
    DOMUtils.safeSetHTML('employee-tags', '');

    // 重置输入框placeholder
    const employeeInput = DOMUtils.safeGetElement('employee-input');
    if (employeeInput) {
        employeeInput.placeholder = '输入姓名搜索...';
        employeeInput.value = '';
    }

    // 重置Sheet显示
    const sheetDisplay = DOMUtils.safeGetElement('sheet-tree-display');
    if (sheetDisplay) {
        const displayText = sheetDisplay.querySelector('.display-text');
        if (displayText) {
            displayText.textContent = '选择Sheet...';
            displayText.className = 'display-text text-gray-500';
        }
    }

    // 重置工资周期显示
    const periodDisplay = DOMUtils.safeGetElement('period-tree-display');
    if (periodDisplay) {
        const displayText = periodDisplay.querySelector('.display-text');
        if (displayText) {
            displayText.textContent = '选择工资周期...';
            displayText.className = 'display-text text-gray-500';
        }
    }
    
    // 重新初始化组件（确保使用原始数据）
    if (allOptions.employees && allOptions.employees.length > 0) {
        initEmployeeAutocomplete(allOptions.employees);
    }
    if (allOptions.sheets && allOptions.sheets.length > 0) {
        initSheetTreeSelect(allOptions.sheets);
    }
    if (allOptions.periods && allOptions.periods.length > 0) {
        initPeriodTreeSelect(allOptions.periods);
    }
    
    // 清空搜索参数并重新搜索所有数据
    currentSearchParams = {};
    
    showSearchLoading();
    
    fetch('/salary/api/search')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSearchResults(data);
                window.history.pushState({}, '', window.location.pathname);
            } else {
                showSearchError(data.error || '重置失败');
            }
        })
        .catch(error => {
            console.error('重置失败:', error);
            showSearchError('重置失败：' + error.message);
        })
        .finally(() => {
            hideSearchLoading();
        });
}

// 设置当前选中的值
function setCurrentValues() {
    const urlParams = new URLSearchParams(window.location.search);
    
    // 设置姓名
    const employeeId = urlParams.get('employee_id');
    if (employeeId) {
        employeeId.split(',').forEach(name => selectedEmployees.add(name.trim()));
        const employeeTagsContainer = document.querySelector('#employee-tags');
        if (employeeTagsContainer) {
            employeeTagsContainer.innerHTML = '';
            selectedEmployees.forEach(employee => {
                const tag = document.createElement('div');
                tag.className = 'inline-flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full';
                tag.innerHTML = `
                    <span>${employee}</span>
                    <button type="button" class="ml-1 text-blue-600 hover:text-blue-800" onclick="removeEmployee('${employee}')">×</button>
                `;
                employeeTagsContainer.appendChild(tag);
            });
        }
    }
    
    // 设置Sheet
    const sheetName = urlParams.get('search_sheet_name') || urlParams.get('sheet_name');
    if (sheetName) {
        sheetName.split(',').forEach(name => selectedSheets.add(name.trim()));
    }
    
    // 设置周期
    const period = urlParams.get('salary_period');
    if (period) {
        period.split(',').forEach(name => selectedPeriods.add(name.trim()));
    }
}

// 页面初始加载时，检查URL参数
function checkInitialParams() {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.toString()) {
        // 有参数，需要执行搜索
        const params = {};
        for (let [key, value] of urlParams.entries()) {
            params[key] = value;
        }
        currentSearchParams = params;
        
        // 触发搜索
        setTimeout(() => {
            const searchUrl = new URL('/salary/api/search', window.location.origin);
            Object.entries(params).forEach(([key, value]) => {
                if (value) {
                    searchUrl.searchParams.set(key, value);
                }
            });
            
            showSearchLoading();
            
            fetch(searchUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateSearchResults(data);
                    } else {
                        showSearchError(data.error || '搜索失败');
                    }
                })
                .catch(error => {
                    console.error('初始搜索失败:', error);
                    showSearchError('搜索失败：' + error.message);
                })
                .finally(() => {
                    hideSearchLoading();
                });
        }, 500); // 等待多选组件初始化完成
    } else {
        // 没有参数，显示所有数据
        fetch('/salary/api/search')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateSearchResults(data);
                }
            })
            .catch(error => {
                console.error('加载初始数据失败:', error);
            });
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置初始tab样式
    const activeTab = document.querySelector('.tab-button.active');
    if (activeTab) {
        activeTab.classList.add('border-blue-500', 'text-blue-600');
        activeTab.classList.remove('border-transparent', 'text-gray-500');
    }

    // 设置其他tab样式
    document.querySelectorAll('.tab-button:not(.active)').forEach(btn => {
        btn.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
    });

    // 修改表单提交行为 - 使用安全的元素获取
    const form = document.querySelector('form');
    if (form) {
        DOMUtils.safeAddEventListener('uploadForm', 'submit', handleFormSubmit) ||
        form.addEventListener('submit', handleFormSubmit);
    }

    // 初始化组件
    initializeComponents();

    // 检查初始参数
    checkInitialParams();
});

// 导出数据功能
function exportData() {
    const params = getSearchParams();

    // 显示全局导出加载状态
    showExportLoading();

    // 构建导出URL
    const exportUrl = new URL('/salary/api/export', window.location.origin);
    Object.entries(params).forEach(([key, value]) => {
        if (value) {
            exportUrl.searchParams.set(key, value);
        }
    });

    // 设置超时保护（最长等待时间）
    // const maxWaitTime = 3600000; // 1小时
    // const timeoutId = setTimeout(() => {
    //     hideExportLoading();
    //     showExportError('导出超时，请重试');
    // }, maxWaitTime);

    // 先发送请求检查是否有错误，然后触发下载
    fetch(exportUrl.toString(), { method: 'HEAD' })
        .then(response => {
            if (!response.ok) {
                // 如果HEAD请求失败，尝试GET请求获取错误信息
                return fetch(exportUrl.toString())
                    .then(res => res.json())
                    .then(data => {
                        throw new Error(data.error || '导出失败');
                    });
            }

            // HEAD请求成功，说明可以下载，创建下载链接
            
            const link = document.createElement('a');
            link.href = exportUrl.toString();
            link.style.display = 'none';
            document.body.appendChild(link);
            
            
            // 触发下载
            link.click();
            hideExportLoading();

            // 清理链接
            document.body.removeChild(link);

            // 清除超时
            // clearTimeout(timeoutId);
        })
        .catch(error => {
            console.error('导出失败:', error);
            // clearTimeout(timeoutId);
            hideExportLoading();
            showExportError(error.message);
        });
}

// 显示导出加载状态 - 全局遮罩层
function showExportLoading() {
    // 创建全局遮罩层
    const overlay = document.createElement('div');
    overlay.id = 'export-loading-overlay';
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    overlay.innerHTML = `
        <div class="bg-white rounded-lg p-8 shadow-xl max-w-sm mx-4">
            <div class="flex flex-col items-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-4 border-blue-600 mb-4"></div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">正在导出数据</h3>
                <p class="text-gray-600 text-center">
                    正在生成Excel文件，请稍候...<br>
                    <span class="text-sm text-gray-500">请不要关闭页面</span>
                </p>
            </div>
        </div>
    `;
    document.body.appendChild(overlay);

    // 防止页面滚动
    document.body.style.overflow = 'hidden';
}

// 隐藏导出加载状态
function hideExportLoading() {
    const overlay = document.getElementById('export-loading-overlay');
    if (overlay) {
        document.body.removeChild(overlay);
        // 恢复页面滚动
        document.body.style.overflow = '';
    }
}

// 显示导出成功提示
function showExportSuccess() {
    showNotification('Excel文件导出成功！', 'success');
}

// 显示导出错误提示
function showExportError(message) {
    showNotification(`导出失败：${message}`, 'error');
}

// 通用通知函数
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-600 text-white' :
        type === 'error' ? 'bg-red-600 text-white' :
        'bg-blue-600 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 3000);
}
</script>

<style>
.tab-button.active {
    border-color: #3B82F6;
    color: #3B82F6;
}
.tab-button:not(.active) {
    border-color: transparent;
    color: #6B7280;
}
.tab-button:not(.active):hover {
    color: #374151;
    border-color: #D1D5DB;
}

/* Autocomplete样式 */
#employee-autocomplete {
    position: relative;
}

#employee-tags {
    min-height: 0;
}

#employee-tags:empty + #employee-input {
    margin-top: 0;
}

/* 树状选择器样式 */
.tree-dropdown {
    z-index: 1000;
}

.toggle-arrow {
    transition: transform 0.2s;
    font-size: 12px;
}

.sheet-children, .period-children {
    transition: all 0.2s ease-in-out;
}

/* 复选框样式 */
input[type="checkbox"] {
    accent-color: #3B82F6;
}

/* 标签样式 */
.tag {
    animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}
</style>
{% endblock %}