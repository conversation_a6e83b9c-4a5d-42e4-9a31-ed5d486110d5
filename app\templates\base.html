<!DOCTYPE html>
<html lang="zh" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% endblock %} - 教职工工资管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-100 h-full flex flex-col">
    <nav class="bg-primary text-white shadow-lg flex-shrink-0">
        <div class="container mx-auto px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="text-xl font-semibold">
                    <a href="{{ url_for('main.index') }}" class="text-white">教职工工资管理系统</a>
                </div>
                <div class="space-x-4">
                    <a href="{{ url_for('salary.upload') }}" class="hover:text-gray-300">导入工资表</a>
                    <a href="{{ url_for('salary.search') }}" class="hover:text-gray-300">查询工资</a>
                    <button id="clear-data-btn" class="hover:text-gray-300 text-red-200 hover:text-red-100">清空数据</button>
                </div>
            </div>
        </div>
    </nav>

    <main class="flex-1 overflow-y-auto">
        <div class="container mx-auto px-6 py-8">
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-4" role="alert">
                            <p>{{ message }}</p>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <footer class="bg-gray-800 text-white flex-shrink-0">
        <div class="container mx-auto px-6 py-4">
            <p class="text-center text-gray-400 text-sm">
                &copy; {{ now.year }} 教职工工资管理系统. All rights reserved.
            </p>
        </div>
    </footer>

    <!-- 清空数据模态框 -->
    <div id="clearDataModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="flex justify-between items-center p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">清空数据</h3>
                    <button onclick="closeClearDataModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <p class="text-gray-700 mb-4">请选择要清空的数据类型：</p>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" id="clear-high" class="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="text-gray-700">中学数据</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="clear-primary" class="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="text-gray-700">小学数据</span>
                            </label>
                        </div>
                    </div>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-red-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <h4 class="text-red-800 font-medium">警告</h4>
                                <p class="text-red-700 text-sm mt-1">此操作将永久删除选中类型的所有工资数据和模板，无法恢复！</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50">
                    <button onclick="closeClearDataModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="confirmClearData()" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700">
                        清除数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认清空数据模态框 -->
    <div id="confirmClearModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <svg class="w-8 h-8 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-900">最终确认</h3>
                    </div>
                    <p class="text-gray-700 mb-4">您确定要清空以下数据吗？</p>
                    <ul id="confirmList" class="list-disc list-inside text-gray-600 mb-6 space-y-1">
                        <!-- 动态填充 -->
                    </ul>
                    <p class="text-red-600 font-medium text-sm">此操作不可逆转！</p>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50">
                    <button onclick="closeConfirmModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="executeClearData()" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700">
                        确认清除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
    // 清空数据相关功能
    document.addEventListener('DOMContentLoaded', function() {
        // 绑定清空数据按钮事件
        document.getElementById('clear-data-btn').addEventListener('click', openClearDataModal);
    });

    function openClearDataModal() {
        document.getElementById('clearDataModal').classList.remove('hidden');
        // 重置复选框
        document.getElementById('clear-high').checked = false;
        document.getElementById('clear-primary').checked = false;
    }

    function closeClearDataModal() {
        document.getElementById('clearDataModal').classList.add('hidden');
    }

    function confirmClearData() {
        const clearHigh = document.getElementById('clear-high').checked;
        const clearPrimary = document.getElementById('clear-primary').checked;
        
        if (!clearHigh && !clearPrimary) {
            alert('请至少选择一种数据类型');
            return;
        }
        
        // 关闭第一个模态框
        closeClearDataModal();
        
        // 准备确认列表
        const confirmList = document.getElementById('confirmList');
        confirmList.innerHTML = '';
        
        if (clearHigh) {
            const li = document.createElement('li');
            li.textContent = '中学数据（包括工资记录和模板）';
            confirmList.appendChild(li);
        }
        
        if (clearPrimary) {
            const li = document.createElement('li');
            li.textContent = '小学数据（包括工资记录和模板）';
            confirmList.appendChild(li);
        }
        
        // 显示确认模态框
        document.getElementById('confirmClearModal').classList.remove('hidden');
    }

    function closeConfirmModal() {
        document.getElementById('confirmClearModal').classList.add('hidden');
    }

    function executeClearData() {
        const clearHigh = document.getElementById('clear-high').checked;
        const clearPrimary = document.getElementById('clear-primary').checked;
        
        // 显示加载状态
        showClearProgress();
        
        // 发送清空请求
        fetch('/salary/api/clear_data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                clear_high: clearHigh,
                clear_primary: clearPrimary
            })
        })
        .then(response => response.json())
        .then(data => {
            hideClearProgress();
            closeConfirmModal();
            
            if (data.success) {
                showSuccessNotification(data.message);
                // 如果当前在查询页面，刷新数据
                if (window.location.pathname.includes('/search')) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                }
            } else {
                showErrorNotification(data.error || '清空数据失败');
            }
        })
        .catch(error => {
            hideClearProgress();
            closeConfirmModal();
            console.error('清空数据失败:', error);
            showErrorNotification('清空数据失败：' + error.message);
        });
    }

    function showClearProgress() {
        const progressDiv = document.createElement('div');
        progressDiv.id = 'clear-progress';
        progressDiv.className = 'fixed top-4 right-4 bg-blue-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm';
        progressDiv.innerHTML = `
            <div class="flex items-center">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                <div>
                    <div class="font-medium">正在清空数据</div>
                    <div class="text-sm opacity-90">请稍候...</div>
                </div>
            </div>
        `;
        document.body.appendChild(progressDiv);
    }

    function hideClearProgress() {
        const progressDiv = document.getElementById('clear-progress');
        if (progressDiv) {
            document.body.removeChild(progressDiv);
        }
    }

    function showSuccessNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm';
        notification.innerHTML = `
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <div>
                    <div class="font-medium">操作成功</div>
                    <div class="text-sm opacity-90">${message}</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 4000);
    }

    function showErrorNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm';
        notification.innerHTML = `
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <div>
                    <div class="font-medium">操作失败</div>
                    <div class="text-sm opacity-90">${message}</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 5000);
    }
    </script>
</body>
</html>