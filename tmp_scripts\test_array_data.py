"""测试数组数据存储"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.excel_parser_v2 import ExcelParserV2

def test_array_data(file_path):
    """测试数组数据解析"""
    print(f"测试文件: {file_path}")
    
    try:
        parser = ExcelParserV2(file_path, header_rows=4)
        result = parser.parse()
        
        for sheet_name, sheet_data in result['sheets'].items():
            print(f"\n=== Sheet: {sheet_name} ===")
            if 'error' in sheet_data:
                print(f"错误: {sheet_data['error']}")
                continue
            
            print(f"数据行数: {sheet_data['row_count']}")
            print(f"姓名列索引: {sheet_data['name_column_index']}")
            
            # 显示列映射
            column_mapping = sheet_data['header_structure']['column_mapping']
            print("列映射:")
            for i, (excel_col, col_name) in enumerate(column_mapping.items()):
                print(f"  数组索引{i}: Excel列{excel_col} -> '{col_name}'")
            
            # 显示前3行数据
            if sheet_data['data']:
                print("\n数据示例 (前3行):")
                for i, row_array in enumerate(sheet_data['data'][:3]):
                    print(f"  行{i+1}: {row_array}")
                    
                    # 显示姓名
                    name_idx = sheet_data['name_column_index']
                    if name_idx is not None and name_idx < len(row_array):
                        print(f"    姓名: {row_array[name_idx]}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_array_data(sys.argv[1])
    else:
        print("用法: python test_array_data.py <excel_file_path>") 