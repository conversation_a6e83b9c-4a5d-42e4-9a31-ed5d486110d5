"""
检查 SalaryData 模型的 template_id 字段
"""

from app import create_app, db
from app.models.salary import SalaryData
from sqlalchemy import text

def check_salary_data():
    app = create_app()
    
    with app.app_context():
        print("检查 SalaryData 模型...")
        
        try:
            # 检查表结构
            columns = db.session.execute(text("PRAGMA table_info(salary_data)")).fetchall()
            print("salary_data 表结构:")
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                not_null = col[3]
                null_info = "NOT NULL" if not_null else "NULL"
                print(f"  - {col_name} ({col_type}) {null_info}")
            
            # 检查是否有数据
            count = SalaryData.query.count()
            print(f"\nsalary_data 表中记录数: {count}")
            
        except Exception as e:
            print(f"✗ 检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_salary_data() 