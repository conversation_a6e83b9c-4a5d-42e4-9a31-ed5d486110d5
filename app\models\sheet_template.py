from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

from app import db

class SheetTemplate(db.Model):
    __tablename__ = 'sheet_template'
    
    id = db.Column(db.Integer, primary_key=True)
    sheet_name = db.Column(db.String(100), nullable=False, index=True)
    template_name = db.Column(db.String(100), nullable=False, default='default')
    school_type = db.Column(db.String(20), nullable=False)  # 新增字段：'high' 或 'primary'
    header_structure = db.Column(db.Text, nullable=False)  # JSON格式的表头结构
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 唯一约束
    __table_args__ = (
        db.UniqueConstraint('sheet_name', 'template_name', name='uk_sheet_template'),
    )
    
    def get_header_structure(self):
        """获取解析后的表头结构"""
        try:
            return json.loads(self.header_structure)
        except (json.JSONDecodeError, TypeError):
            return None
    
    def set_header_structure(self, structure):
        """设置表头结构"""
        self.header_structure = json.dumps(structure, ensure_ascii=False)
    
    def __repr__(self):
        return f'<SheetTemplate {self.template_name} - {self.sheet_name}>'