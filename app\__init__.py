from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from config import Config
import os
import logging
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from datetime import datetime
from app.logging_config import setup_advanced_logging

db = SQLAlchemy()
migrate = Migrate()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    
    # 添加自定义过滤器
    @app.template_filter('is_number')
    def is_number(value):
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False
    
    @app.template_filter('format_number')
    def format_number(value):
        try:
            num = float(value)
            # 如果是整数，不显示小数点
            if num == int(num):
                return str(int(num))
            else:
                return "{:.2f}".format(num)
        except (ValueError, TypeError):
            return value
    
    # 在现有的过滤器定义后添加全局函数
    @app.template_global()
    def min(a, b):
        """在模板中提供min函数"""
        return a if a < b else b
    
    @app.template_global() 
    def max(a, b):
        """在模板中提供max函数"""
        return a if a > b else b
    
    # 注册蓝图
    from app.routes import main, salary
    app.register_blueprint(main.bp)
    app.register_blueprint(salary.bp)
    
    # 确保实例文件夹存在
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass
    
    # 配置日志
    setup_advanced_logging(app)
        
    return app

def setup_logging(app):
    """配置日志系统"""
    
    # 确保日志目录存在
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s %(levelname)s %(name)s: %(message)s [in %(pathname)s:%(lineno)d]'
    )
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 清除现有的处理器（避免重复）
    root_logger.handlers.clear()
    
    # 1. 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 2. 按日期轮转的文件处理器
    today = datetime.now().strftime('%Y-%m-%d')
    daily_log_file = os.path.join(log_dir, f'salary_manager_{today}.log')
    
    # 使用 TimedRotatingFileHandler 按天轮转
    file_handler = TimedRotatingFileHandler(
        filename=os.path.join(log_dir, 'salary_manager.log'),
        when='midnight',  # 每天午夜轮转
        interval=1,       # 间隔1天
        backupCount=30,   # 保留30天的日志
        encoding='utf-8'
    )
    
    # 设置文件名后缀格式
    file_handler.suffix = '%Y-%m-%d'
    
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # 3. 错误日志单独文件（可选）
    error_handler = TimedRotatingFileHandler(
        filename=os.path.join(log_dir, 'salary_manager_error.log'),
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    error_handler.suffix = '%Y-%m-%d'
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    root_logger.addHandler(error_handler)
    
    # 配置 Flask 应用的日志
    app.logger.setLevel(logging.INFO)
    
    # 配置第三方库的日志级别（减少噪音）
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    app.logger.info('工资管理系统启动 - 日志系统已配置')
    app.logger.info(f'日志文件保存在: {os.path.abspath(log_dir)}') 