"""
使用Flask-Migrate创建迁移
"""

from app import create_app
from flask_migrate import init, migrate, upgrade
import os

app = create_app()

with app.app_context():
    # 检查migrations文件夹是否存在
    if not os.path.exists('migrations'):
        print("初始化Flask-Migrate...")
        init()
    
    print("创建迁移文件...")
    migrate(message="Add sheet_name to excel_template")
    
    print("应用迁移...")
    upgrade()
    
    print("迁移完成!") 