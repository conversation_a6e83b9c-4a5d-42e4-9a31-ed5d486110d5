{% extends "base.html" %}

{% block title %}导入工资表{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-800 mb-8">导入工资表</h1>
    
    <div class="bg-white rounded-lg shadow-md p-6">
        <form method="post" action="{{ url_for('salary.upload') }}" enctype="multipart/form-data" class="space-y-6" id="uploadForm">
            <div>
                <label class="block text-gray-700 text-sm font-bold mb-2">选择学校类型</label>
                <select name="school_type" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="">请选择...</option>
                    <option value="high">中学</option>
                    <option value="primary">小学</option>
                </select>
            </div>
            
            <div>
                <label class="block text-gray-700 text-sm font-bold mb-2">表头行数</label>
                <input type="number" name="header_rows" value="4" min="1" max="10" required 
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                >
                <p class="mt-1 text-sm text-gray-500">请输入Excel表格中表头的行数（默认为4行）</p>
            </div>
            
            <div class="relative">
                <label class="block text-gray-700 text-sm font-bold mb-2">选择工资表文件</label>
                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-primary transition-colors duration-200" id="dropZone">
                    <div class="space-y-1 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="flex text-sm text-gray-600">
                            <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-primary hover:text-blue-800">
                                <span>选择文件</span>
                                <input id="file-upload" name="file" type="file" class="sr-only" multiple accept=".xlsx,.xls" required>
                            </label>
                            <p class="pl-1">或拖放文件到这里</p>
                        </div>
                        <p class="text-xs text-gray-500" id="fileInfo">支持 Excel 文件 (.xlsx, .xls)</p>
                    </div>
                </div>
            </div>
            
            <div class="hidden" id="uploadProgress">
                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mb-2">
                    <div class="bg-primary h-2.5 rounded-full transition-colors duration-300" id="progressBar" style="width: 0%"></div>
                </div>
                <p class="text-sm text-gray-600" id="progressText">准备上传...</p>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-800 transition-colors" id="submitBtn">
                    开始导入
                </button>
            </div>
        </form>
    </div>
    
    <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">导入说明</h2>
        <div class="space-y-4 text-gray-600">
            <p>1. 请选择正确的学校类型（中学/小学）。</p>
            <p>2. 支持同时选择多个工资表文件进行导入。</p>
            <p>3. 工资表文件名建议使用"年月"格式，如：2023年09月工资表.xlsx。</p>
            <p>4. 系统会自动处理表格中的多行表头结构。</p>
            <p>5. 导入成功后，数据将自动保存到系统中。</p>
        </div>
    </div>
    
    <!-- 调试信息区域 -->
    <div class="mt-8 bg-white rounded-lg shadow-md p-6" id="debugInfo">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">调试信息</h2>
        <pre class="bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-96"></pre>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropZone = DOMUtils.safeGetElement('dropZone');
    const fileInput = DOMUtils.safeGetElement('file-upload');
    const fileInfo = DOMUtils.safeGetElement('fileInfo');
    const form = DOMUtils.safeGetElement('uploadForm');
    const debugInfo = document.querySelector('#debugInfo pre');

    // 检查必要元素是否存在
    if (!dropZone || !fileInput || !form) {
        console.error('Upload page: Required elements not found');
        return;
    }
    
    function log(message) {
        const timestamp = new Date().toISOString();
        debugInfo.textContent += `[${timestamp}] ${message}\n`;
        debugInfo.scrollTop = debugInfo.scrollHeight;
        console.log(message);
    }
    
    // 显示成功提醒函数
    function showSuccessNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm';
        notification.innerHTML = `
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <div>
                    <div class="font-medium">Excel数据导入成功</div>
                    <div class="text-sm opacity-90">${message}</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 添加进入动画
        notification.style.transform = 'translateX(100%)';
        notification.style.transition = 'transform 0.3s ease-in-out';
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // 4秒后自动消失
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }
    
    // 重置表单函数
    function resetForm() {
        // 重置学校类型选择
        form.querySelector('select[name="school_type"]').value = '';
        
        // 重置表头行数
        form.querySelector('input[name="header_rows"]').value = '4';
        
        // 重置文件输入
        fileInput.value = '';
        fileInfo.textContent = '支持 Excel 文件 (.xlsx, .xls)';
        
        // 隐藏进度条
        DOMUtils.safeToggleClass('uploadProgress', 'hidden', true);

        // 重置拖放区域样式
        if (dropZone) {
            dropZone.classList.remove('border-primary', 'bg-blue-50');
        }

        // 启用提交按钮
        DOMUtils.safeSetProperty('submitBtn', 'disabled', false);
        
        log('表单已重置');
    }
    
    // 文件大小限制（10MB）
    const MAX_FILE_SIZE = 10 * 1024 * 1024;
    const ALLOWED_TYPES = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];

    // 阻止默认拖放行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        if (dropZone) {
            dropZone.addEventListener(eventName, preventDefaults, false);
        }
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // 添加拖放区域的视觉反馈
    ['dragenter', 'dragover'].forEach(eventName => {
        if (dropZone) {
            dropZone.addEventListener(eventName, highlight, false);
        }
    });

    ['dragleave', 'drop'].forEach(eventName => {
        if (dropZone) {
            dropZone.addEventListener(eventName, unhighlight, false);
        }
    });

    function highlight(e) {
        if (dropZone) {
            dropZone.classList.add('border-primary', 'bg-blue-50');
        }
    }

    function unhighlight(e) {
        if (dropZone) {
            dropZone.classList.remove('border-primary', 'bg-blue-50');
        }
    }

    // 处理文件拖放
    if (dropZone) {
        dropZone.addEventListener('drop', handleDrop, false);
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles(files);
    }

    // 处理文件选择
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            handleFiles(this.files);
        });
    }

    function handleFiles(files) {
        const validFiles = [];
        const errors = [];
        
        Array.from(files).forEach(file => {
            log(`检查文件：${file.name}, 类型：${file.type}, 大小：${file.size} 字节`);
            
            // 检查文件类型
            const isExcelFile = ALLOWED_TYPES.includes(file.type) || 
                              file.name.endsWith('.xlsx') || 
                              file.name.endsWith('.xls');
                              
            if (!isExcelFile) {
                const error = `文件 ${file.name} 类型不支持，仅支持Excel文件`;
                errors.push(error);
                log(error);
                return;
            }
            
            if (file.size > MAX_FILE_SIZE) {
                const error = `文件 ${file.name} 超过大小限制（10MB）`;
                errors.push(error);
                log(error);
                return;
            }
            
            validFiles.push(file);
            log(`文件 ${file.name} 验证通过`);
        });
        
        if (errors.length > 0) {
            alert(errors.join('\n'));
            fileInput.value = '';
            fileInfo.textContent = '支持 Excel 文件 (.xlsx, .xls)';
            return;
        }
        
        if (validFiles.length > 0) {
            const fileNames = validFiles.map(f => f.name).join(', ');
            fileInfo.textContent = `已选择 ${validFiles.length} 个文件: ${fileNames}`;
            log(`已选择文件：${fileNames}`);
            
            // 创建新的 FileList 对象
            const dataTransfer = new DataTransfer();
            validFiles.forEach(file => dataTransfer.items.add(file));
            fileInput.files = dataTransfer.files;
        }
    }

    // 点击整个区域触发文件选择
    dropZone.addEventListener('click', function(e) {
        if (e.target !== fileInput) {
            fileInput.click();
        }
    });

    // 表单提交处理
    if (form) {
        form.addEventListener('submit', async function(e) {
        e.preventDefault();
        log('表单提交开始');
        
        const schoolType = form.querySelector('select[name="school_type"]').value;
        if (!schoolType) {
            alert('请选择学校类型');
            log('错误：未选择学校类型');
            return;
        }

        if (!fileInput.files || fileInput.files.length === 0) {
            alert('请选择至少一个文件');
            log('错误：未选择文件');
            return;
        }

        const submitBtn = DOMUtils.safeGetElement('submitBtn');
        const progressDiv = DOMUtils.safeGetElement('uploadProgress');
        const progressBar = DOMUtils.safeGetElement('progressBar');
        const progressText = DOMUtils.safeGetElement('progressText');

        DOMUtils.safeSetProperty('submitBtn', 'disabled', true);
        DOMUtils.safeToggleClass('uploadProgress', 'hidden', false);

        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.style.backgroundColor = '#1e40af'; // 重置为蓝色
        }
        if (progressText) {
            progressText.textContent = '准备上传...';
        }

        const formData = new FormData(form);
        log(`准备发送请求，文件数量：${formData.getAll('file').length}`);
        
        try {
            if (progressBar) {
                progressBar.style.width = '10%';
            }
            if (progressText) {
                progressText.textContent = '正在上传...';
            }

            log(`发送请求到：${form.action}`);
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            log(`收到响应，状态码：${response.status}`);
            if (progressBar) {
                progressBar.style.width = '50%';
            }

            if (response.ok) {
                const result = await response.json();
                log(`响应数据：${JSON.stringify(result)}`);

                if (result.success) {
                    // 成功时设置绿色进度条
                    if (progressBar) {
                        progressBar.style.backgroundColor = '#10b981';
                        progressBar.style.width = '100%';
                    }
                    if (progressText) {
                        progressText.textContent = '上传成功！';
                    }
                    
                    // 构建成功消息
                    let successMessage = '';
                    const details = result.details;
                    if (details.success_files.length > 0) {
                        successMessage = `成功导入 ${details.success_files.length} 个文件`;
                        progressText.textContent += ` 成功导入 ${details.success_files.length} 个文件`;
                        log(`成功导入 ${details.success_files.length} 个文件：${details.success_files.join(', ')}`);
                    }
                    if (details.error_files.length > 0) {
                        progressText.textContent += `\n导入失败 ${details.error_files.length} 个文件`;
                        details.error_files.forEach(error => {
                            progressText.textContent += `\n${error.file}: ${error.error}`;
                            log(`文件导入失败：${error.file} - ${error.error}`);
                        });
                    }
                    
                    // 显示成功提醒
                    showSuccessNotification(successMessage);
                    
                    // 3秒后重置表单（而不是刷新页面）
                    setTimeout(() => {
                        resetForm();
                    }, 3000);
                } else {
                    throw new Error(result.message || '上传失败');
                }
            } else {
                throw new Error(`服务器响应错误：${response.status}`);
            }
        } catch (error) {
            log(`上传过程中发生错误：${error.message}`);
            progressText.textContent = `错误：${error.message}`;
            progressBar.style.backgroundColor = '#ef4444';
            progressBar.style.width = '100%';
            submitBtn.disabled = false;
        }
    });
});
</script>
{% endblock %}